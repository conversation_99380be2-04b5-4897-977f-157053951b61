//
//  HabitTemplates.swift
//  LightningHabit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 23..
//

import SwiftUI

// MARK: - Template Category

enum TemplateCategory: String, CaseIterable {
    case health = "health"
    case fitness = "fitness"
    case productivity = "productivity"
    case learning = "learning"
    case creative = "creative"
    
    var localizedName: String {
        return rawValue.localized
    }
    
    var icon: String {
        switch self {
        case .health: return "heart.fill"
        case .fitness: return "figure.run"
        case .productivity: return "target"
        case .learning: return "brain.head.profile"
        case .creative: return "paintbrush.fill"
        }
    }
    
    var color: Color {
        switch self {
        case .health: return .red
        case .fitness: return .green
        case .productivity: return .blue
        case .learning: return .purple
        case .creative: return .orange
        }
    }
}

// MARK: - Habit Template Model

struct HabitTemplate: Identifiable, Hashable {
    let id = UUID()
    let name: String
    let icon: String
    let description: String
    let category: TemplateCategory
    let defaultReminderEnabled: Bool
    let defaultReminderTime: Date?
    let popularity: Int
    
    init(name: String, icon: String, description: String, category: TemplateCategory, defaultReminderEnabled: Bool = false, defaultReminderTime: Date? = nil, popularity: Int = 0) {
        self.name = name
        self.icon = icon
        self.description = description
        self.category = category
        self.defaultReminderEnabled = defaultReminderEnabled
        self.defaultReminderTime = defaultReminderTime
        self.popularity = popularity
    }
    
    func hash(into hasher: inout Hasher) {
        hasher.combine(id)
    }
    
    static func == (lhs: HabitTemplate, rhs: HabitTemplate) -> Bool {
        return lhs.id == rhs.id
    }
}

// MARK: - Template Data

extension HabitTemplate {
    static let allTemplates: [HabitTemplate] = [
        // Health Templates
        HabitTemplate(name: "habit_drink_water".localized, icon: "drop.fill", description: "habit_drink_water_desc".localized, category: .health, defaultReminderEnabled: true, defaultReminderTime: Calendar.current.date(from: DateComponents(hour: 9, minute: 0)), popularity: 10),
        HabitTemplate(name: "habit_take_vitamins".localized, icon: "pills.fill", description: "habit_take_vitamins_desc".localized, category: .health, defaultReminderEnabled: true, defaultReminderTime: Calendar.current.date(from: DateComponents(hour: 8, minute: 0)), popularity: 8),
        HabitTemplate(name: "habit_brush_teeth".localized, icon: "mouth.fill", description: "habit_brush_teeth_desc".localized, category: .health, defaultReminderEnabled: true, defaultReminderTime: Calendar.current.date(from: DateComponents(hour: 21, minute: 0)), popularity: 9),
        HabitTemplate(name: "habit_sleep_8_hours".localized, icon: "bed.double.fill", description: "habit_sleep_8_hours_desc".localized, category: .health, defaultReminderEnabled: true, defaultReminderTime: Calendar.current.date(from: DateComponents(hour: 22, minute: 0)), popularity: 7),
        HabitTemplate(name: "habit_eat_fruit".localized, icon: "apple.logo", description: "habit_eat_fruit_desc".localized, category: .health, popularity: 6),
        HabitTemplate(name: "habit_meditate".localized, icon: "leaf.fill", description: "habit_meditate_desc".localized, category: .health, defaultReminderEnabled: true, defaultReminderTime: Calendar.current.date(from: DateComponents(hour: 7, minute: 30)), popularity: 9),
        HabitTemplate(name: "habit_eat_vegetables".localized, icon: "carrot.fill", description: "habit_eat_vegetables_desc".localized, category: .health, popularity: 7),
        HabitTemplate(name: "habit_take_deep_breaths".localized, icon: "lungs.fill", description: "habit_take_deep_breaths_desc".localized, category: .health, popularity: 6),
        HabitTemplate(name: "habit_stand_every_hour".localized, icon: "figure.stand", description: "habit_stand_every_hour_desc".localized, category: .health, popularity: 5),
        HabitTemplate(name: "habit_limit_caffeine".localized, icon: "cup.and.saucer.fill", description: "habit_limit_caffeine_desc".localized, category: .health, popularity: 4),
        HabitTemplate(name: "habit_skincare_routine".localized, icon: "face.smiling.fill", description: "habit_skincare_routine_desc".localized, category: .health, popularity: 6),
        HabitTemplate(name: "habit_sunlight_exposure".localized, icon: "sun.max.fill", description: "habit_sunlight_exposure_desc".localized, category: .health, popularity: 5),
        
        // Fitness Templates  
        HabitTemplate(name: "Morning Run", icon: "figure.run", description: "Start day with cardio", category: .fitness, defaultReminderEnabled: true, defaultReminderTime: Calendar.current.date(from: DateComponents(hour: 7, minute: 0)), popularity: 9),
        HabitTemplate(name: "Do Push-ups", icon: "dumbbell.fill", description: "Quick strength training", category: .fitness, popularity: 7),
        HabitTemplate(name: "Walk 10k Steps", icon: "figure.walk", description: "Daily step goal", category: .fitness, popularity: 8),
        HabitTemplate(name: "Daily Stretch", icon: "figure.yoga", description: "Improve flexibility", category: .fitness, defaultReminderEnabled: true, defaultReminderTime: Calendar.current.date(from: DateComponents(hour: 19, minute: 0)), popularity: 6),
        HabitTemplate(name: "Gym Session", icon: "sportscourt.fill", description: "Full workout routine", category: .fitness, popularity: 5),
        HabitTemplate(name: "Plank Exercise", icon: "figure.strengthtraining.traditional", description: "Core strengthening", category: .fitness, popularity: 6),
        HabitTemplate(name: "Bike Ride", icon: "bicycle", description: "Cycling workout", category: .fitness, popularity: 5),
        HabitTemplate(name: "Swimming", icon: "figure.pool.swim", description: "Full body workout", category: .fitness, popularity: 4),
        HabitTemplate(name: "Squat Challenge", icon: "figure.squat", description: "Lower body strength", category: .fitness, popularity: 5),
        HabitTemplate(name: "Dance Practice", icon: "figure.dance", description: "Fun cardio workout", category: .fitness, popularity: 4),
        HabitTemplate(name: "Stairs Instead Elevator", icon: "figure.stairs", description: "Active lifestyle choice", category: .fitness, popularity: 3),
        HabitTemplate(name: "Hiking", icon: "figure.hiking", description: "Nature workout", category: .fitness, popularity: 4),
        
        // Productivity Templates
        HabitTemplate(name: "Plan Tomorrow", icon: "calendar", description: "Prepare for next day", category: .productivity, defaultReminderEnabled: true, defaultReminderTime: Calendar.current.date(from: DateComponents(hour: 20, minute: 0)), popularity: 8),
        HabitTemplate(name: "Clear Email Inbox", icon: "envelope.fill", description: "Stay organized", category: .productivity, defaultReminderEnabled: true, defaultReminderTime: Calendar.current.date(from: DateComponents(hour: 17, minute: 0)), popularity: 7),
        HabitTemplate(name: "Review Goals", icon: "target", description: "Track progress", category: .productivity, popularity: 6),
        HabitTemplate(name: "No Social Media Hour", icon: "iphone.slash", description: "Digital detox time", category: .productivity, popularity: 5),
        HabitTemplate(name: "Time Blocking", icon: "clock.fill", description: "Schedule focused work", category: .productivity, popularity: 4),
        HabitTemplate(name: "Clean Workspace", icon: "archivebox.fill", description: "Organized environment", category: .productivity, popularity: 6),
        HabitTemplate(name: "Morning Review", icon: "list.clipboard.fill", description: "Start day prepared", category: .productivity, defaultReminderEnabled: true, defaultReminderTime: Calendar.current.date(from: DateComponents(hour: 8, minute: 30)), popularity: 7),
        HabitTemplate(name: "Pomodoro Session", icon: "timer", description: "Focused work blocks", category: .productivity, popularity: 5),
        HabitTemplate(name: "Weekly Planning", icon: "calendar.badge.plus", description: "Plan your week", category: .productivity, popularity: 4),
        HabitTemplate(name: "Backup Files", icon: "externaldrive.fill", description: "Data protection", category: .productivity, popularity: 3),
        HabitTemplate(name: "Single Task Focus", icon: "target", description: "Avoid multitasking", category: .productivity, popularity: 5),
        HabitTemplate(name: "Update Task List", icon: "checklist", description: "Track your tasks", category: .productivity, popularity: 4),
        
        // Learning Templates
        HabitTemplate(name: "Read 20 Minutes", icon: "book.fill", description: "Daily reading habit", category: .learning, defaultReminderEnabled: true, defaultReminderTime: Calendar.current.date(from: DateComponents(hour: 21, minute: 0)), popularity: 9),
        HabitTemplate(name: "Practice Language", icon: "textbook.fill", description: "Language learning", category: .learning, popularity: 7),
        HabitTemplate(name: "Learn New Skill", icon: "graduationcap.fill", description: "Continuous learning", category: .learning, popularity: 6),
        HabitTemplate(name: "Brain Training", icon: "brain.head.profile", description: "Mental exercises", category: .learning, popularity: 5),
        HabitTemplate(name: "Educational Video", icon: "play.rectangle.fill", description: "Watch and learn", category: .learning, popularity: 4),
        HabitTemplate(name: "Listen to Podcast", icon: "headphones", description: "Learn while commuting", category: .learning, popularity: 6),
        HabitTemplate(name: "Practice Coding", icon: "chevron.left.forwardslash.chevron.right", description: "Programming practice", category: .learning, popularity: 5),
        HabitTemplate(name: "Study Flashcards", icon: "rectangle.stack.fill", description: "Memory reinforcement", category: .learning, popularity: 4),
        HabitTemplate(name: "Take Notes", icon: "note.text", description: "Capture learning", category: .learning, popularity: 5),
        HabitTemplate(name: "Online Course", icon: "laptopcomputer", description: "Structured learning", category: .learning, popularity: 4),
        HabitTemplate(name: "Wikipedia Article", icon: "globe", description: "Random knowledge", category: .learning, popularity: 3),
        HabitTemplate(name: "TED Talk", icon: "play.circle.fill", description: "Inspiring ideas", category: .learning, popularity: 4),
        
        // Creative Templates
        HabitTemplate(name: "Journal Writing", icon: "pencil.line", description: "Express thoughts", category: .creative, defaultReminderEnabled: true, defaultReminderTime: Calendar.current.date(from: DateComponents(hour: 22, minute: 0)), popularity: 8),
        HabitTemplate(name: "Sketch Daily", icon: "paintbrush.fill", description: "Creative drawing", category: .creative, popularity: 6),
        HabitTemplate(name: "Practice Music", icon: "music.note", description: "Musical practice", category: .creative, popularity: 5),
        HabitTemplate(name: "Take Photos", icon: "camera.fill", description: "Daily photography", category: .creative, popularity: 4),
        HabitTemplate(name: "Creative Writing", icon: "doc.text.fill", description: "Write creatively", category: .creative, popularity: 3),
        HabitTemplate(name: "Play Instrument", icon: "pianokeys", description: "Musical instrument practice", category: .creative, popularity: 5),
        HabitTemplate(name: "Doodle Break", icon: "scribble", description: "Quick creative sketching", category: .creative, popularity: 4),
        HabitTemplate(name: "Recipe Experiment", icon: "frying.pan", description: "Culinary creativity", category: .creative, popularity: 4),
        HabitTemplate(name: "Craft Project", icon: "scissors", description: "Hands-on creativity", category: .creative, popularity: 3),
        HabitTemplate(name: "Poetry Writing", icon: "quote.bubble.fill", description: "Express through verse", category: .creative, popularity: 3),
        HabitTemplate(name: "Color Something", icon: "paintpalette.fill", description: "Relaxing coloring", category: .creative, popularity: 4),
        HabitTemplate(name: "Design Practice", icon: "rectangle.3.group.fill", description: "Visual design skills", category: .creative, popularity: 3),
        HabitTemplate(name: "Creative Challenge", icon: "sparkles", description: "Daily creative prompt", category: .creative, popularity: 4)
    ]
    
    static func templates(for category: TemplateCategory) -> [HabitTemplate] {
        return allTemplates.filter { $0.category == category }.sorted { $0.popularity > $1.popularity }
    }
    
    static func search(_ query: String) -> [HabitTemplate] {
        guard !query.isEmpty else { return allTemplates.sorted { $0.popularity > $1.popularity } }
        
        return allTemplates.filter { template in
            template.name.localizedCaseInsensitiveContains(query) ||
            template.description.localizedCaseInsensitiveContains(query) ||
            template.category.rawValue.localizedCaseInsensitiveContains(query)
        }.sorted { $0.popularity > $1.popularity }
    }
    
    static var featuredTemplates: [HabitTemplate] {
        return allTemplates.sorted { $0.popularity > $1.popularity }.prefix(6).map { $0 }
    }
}
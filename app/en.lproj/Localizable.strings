/* 
  Localizable.strings
  LightningHabit
  
  English localization
*/

// MARK: - General
"app_name" = "LightningHabit";
"save" = "Save";
"cancel" = "Cancel";
"delete" = "Delete";
"edit" = "Edit";
"done" = "Done";
"ok" = "OK";
"select" = "Select";
"deselect_all" = "Deselect All";
"select_all" = "Select All";

// MARK: - Navigation
"home" = "Home";
"activity" = "Activity";
"settings" = "Settings";
"manage_habits" = "Manage Habits";

// MARK: - Habits
"new_habit" = "New Habit";
"edit_habit" = "Edit Habit";
"add_habit" = "Add Habit";
"add_your_first_habit" = "Add Your First Habit";
"no_habits_yet" = "No habits yet";
"start_building_routine" = "Start building your routine";
"habit_name" = "Habit Name";
"habit_name_placeholder" = "e.g., Morning Meditation";
"choose_icon" = "Choose Icon";
"more_habits" = "More Habits";
"popular_habit_templates" = "Popular habit templates to get started";
"template" = "TEMPLATE";
"choose_template" = "Choose a Template";

// MARK: - Habit Actions
"delete_habit_question" = "Delete Habit?";
"delete_habit_message" = "This will permanently delete \"%@\" and all its data.";
"delete_habits_question" = "Delete %d Habits?";
"delete_all_habits_message" = "This will permanently delete all selected habits and their data.";
"delete_all" = "Delete All";
"duplicate_habit" = "Duplicate Habit";
"duplicate_habit_message" = "A habit named \"%@\" already exists.";

// MARK: - Categories
"wellness" = "Wellness";
"fitness" = "Fitness";
"learning" = "Learning";
"productivity" = "Productivity";
"health" = "Health";
"creative" = "Creative";

// MARK: - Reminders
"reminder_settings" = "Reminder Settings";
"custom_time" = "Custom Time";
"morning" = "Morning";
"noon" = "Noon";
"evening" = "Evening";
"night" = "Night";

// MARK: - Statistics
"current_streak" = "Current Streak";
"best_streak" = "Best Streak";
"total_completions" = "Total Completions";
"completion_rate" = "Completion Rate";
"last_7_days" = "Last 7 Days";
"day_streak" = "%d day streak";
"days_streak" = "%d days streak";
"completed_of" = "%d of %d completed";

// MARK: - Export
"export_data" = "Export Data";
"export_as_json" = "Export as JSON";
"export_as_csv" = "Export as CSV";

// MARK: - Settings
"privacy_policy" = "Privacy Policy";
"show_template_cards" = "Show Template Cards";
"contact_support" = "Contact Support";
"rate_app" = "Rate App";
"version" = "Version %@";

// MARK: - Completion
"amazing" = "Amazing!";
"well_done" = "Well done!";
"keep_going" = "Keep it going!";
"great_job" = "Great job!";
"youre_on_fire" = "You're on fire!";
"all_habits_completed" = "All habits completed!";
"take_a_break" = "Time to take a break and celebrate your wins";

// MARK: - Activity View
"activity" = "Activity";
"no_activity_yet" = "No activity yet";
"complete_first_habit" = "Complete your first habit to see your progress";
"todays_habits" = "Today's Habits";
"your_progress" = "Your Progress";
"monthly_overview" = "Monthly Overview";
"sun" = "Sun";
"mon" = "Mon";
"tue" = "Tue";
"wed" = "Wed";
"thu" = "Thu";
"fri" = "Fri";
"sat" = "Sat";

// MARK: - Notifications
"time_for_habit" = "Time for %@!";
"dont_forget_habit" = "Don't forget to complete your habit today 💪";

// MARK: - Error Messages
"error_loading_data" = "Error loading data";
"error_saving_data" = "Error saving data";
"try_again" = "Try Again";

// MARK: - Swipe Actions
"skip" = "Skip";
"undo" = "Undo";

// MARK: - Time Descriptions
"morning_habit" = "Morning habit";
"mid_morning" = "Mid-morning";
"lunch_time" = "Lunch time";
"afternoon" = "Afternoon";
"evening" = "Evening";
"night_routine" = "Night routine";
"daily_habit" = "Daily habit";
"due_soon" = "Due soon";

// MARK: - Habit Templates
// Health
"habit_drink_water" = "Drink Water";
"habit_drink_water_desc" = "Stay hydrated daily";
"habit_take_vitamins" = "Take Vitamins";
"habit_take_vitamins_desc" = "Daily vitamin routine";
"habit_brush_teeth" = "Brush Teeth";
"habit_brush_teeth_desc" = "Oral hygiene routine";
"habit_sleep_8_hours" = "Sleep 8 Hours";
"habit_sleep_8_hours_desc" = "Quality rest time";
"habit_eat_fruit" = "Eat Fruit";
"habit_eat_fruit_desc" = "Daily fruit intake";
"habit_meditate" = "Meditate";
"habit_meditate_desc" = "Daily mindfulness practice";
"habit_eat_vegetables" = "Eat Vegetables";
"habit_eat_vegetables_desc" = "Daily veggie intake";
"habit_take_deep_breaths" = "Take Deep Breaths";
"habit_take_deep_breaths_desc" = "Breathing exercises";
"habit_stand_every_hour" = "Stand Every Hour";
"habit_stand_every_hour_desc" = "Combat sitting";
"habit_limit_caffeine" = "Limit Caffeine";
"habit_limit_caffeine_desc" = "Healthy caffeine intake";
"habit_skincare_routine" = "Skincare Routine";
"habit_skincare_routine_desc" = "Daily skincare";
"habit_sunlight_exposure" = "Sunlight Exposure";
"habit_sunlight_exposure_desc" = "Get natural light";

// Fitness
"habit_morning_run" = "Morning Run";
"habit_morning_run_desc" = "Start day with cardio";
"habit_do_pushups" = "Do Push-ups";
"habit_do_pushups_desc" = "Quick strength training";
"habit_walk_10k_steps" = "Walk 10k Steps";
"habit_walk_10k_steps_desc" = "Daily step goal";
"habit_daily_stretch" = "Daily Stretch";
"habit_daily_stretch_desc" = "Improve flexibility";
"habit_gym_session" = "Gym Session";
"habit_gym_session_desc" = "Full workout routine";
"habit_plank_exercise" = "Plank Exercise";
"habit_plank_exercise_desc" = "Core strengthening";
"habit_bike_ride" = "Bike Ride";
"habit_bike_ride_desc" = "Cycling workout";
"habit_swimming" = "Swimming";
"habit_swimming_desc" = "Full body workout";
"habit_squat_challenge" = "Squat Challenge";
"habit_squat_challenge_desc" = "Lower body strength";
"habit_dance_practice" = "Dance Practice";
"habit_dance_practice_desc" = "Fun cardio workout";
"habit_stairs_instead_elevator" = "Stairs Instead Elevator";
"habit_stairs_instead_elevator_desc" = "Active lifestyle choice";
"habit_hiking" = "Hiking";
"habit_hiking_desc" = "Nature workout";

// Productivity
"habit_plan_tomorrow" = "Plan Tomorrow";
"habit_plan_tomorrow_desc" = "Prepare for next day";
"habit_clear_email_inbox" = "Clear Email Inbox";
"habit_clear_email_inbox_desc" = "Stay organized";
"habit_review_goals" = "Review Goals";
"habit_review_goals_desc" = "Track progress";
"habit_no_social_media_hour" = "No Social Media Hour";
"habit_no_social_media_hour_desc" = "Digital detox time";
"habit_time_blocking" = "Time Blocking";
"habit_time_blocking_desc" = "Schedule focused work";
"habit_clean_workspace" = "Clean Workspace";
"habit_clean_workspace_desc" = "Organized environment";
"habit_morning_review" = "Morning Review";
"habit_morning_review_desc" = "Start day prepared";
"habit_pomodoro_session" = "Pomodoro Session";
"habit_pomodoro_session_desc" = "Focused work blocks";
"habit_weekly_planning" = "Weekly Planning";
"habit_weekly_planning_desc" = "Plan your week";
"habit_backup_files" = "Backup Files";
"habit_backup_files_desc" = "Data protection";
"habit_single_task_focus" = "Single Task Focus";
"habit_single_task_focus_desc" = "Avoid multitasking";
"habit_update_task_list" = "Update Task List";
"habit_update_task_list_desc" = "Track your tasks";

// Learning
"habit_read_20_minutes" = "Read 20 Minutes";
"habit_read_20_minutes_desc" = "Daily reading habit";
"habit_practice_language" = "Practice Language";
"habit_practice_language_desc" = "Language learning";
"habit_learn_new_skill" = "Learn New Skill";
"habit_learn_new_skill_desc" = "Continuous learning";
"habit_brain_training" = "Brain Training";
"habit_brain_training_desc" = "Mental exercises";
"habit_educational_video" = "Educational Video";
"habit_educational_video_desc" = "Watch and learn";
"habit_listen_to_podcast" = "Listen to Podcast";
"habit_listen_to_podcast_desc" = "Learn while commuting";
"habit_practice_coding" = "Practice Coding";
"habit_practice_coding_desc" = "Programming practice";
"habit_study_flashcards" = "Study Flashcards";
"habit_study_flashcards_desc" = "Memory reinforcement";
"habit_take_notes" = "Take Notes";
"habit_take_notes_desc" = "Capture learning";
"habit_online_course" = "Online Course";
"habit_online_course_desc" = "Structured learning";
"habit_wikipedia_article" = "Wikipedia Article";
"habit_wikipedia_article_desc" = "Random knowledge";
"habit_ted_talk" = "TED Talk";
"habit_ted_talk_desc" = "Inspiring ideas";

// Creative
"habit_journal_writing" = "Journal Writing";
"habit_journal_writing_desc" = "Express thoughts";
"habit_sketch_daily" = "Sketch Daily";
"habit_sketch_daily_desc" = "Creative drawing";
"habit_practice_music" = "Practice Music";
"habit_practice_music_desc" = "Musical practice";
"habit_take_photos" = "Take Photos";
"habit_take_photos_desc" = "Daily photography";
"habit_creative_writing" = "Creative Writing";
"habit_creative_writing_desc" = "Write creatively";
"habit_play_instrument" = "Play Instrument";
"habit_play_instrument_desc" = "Musical instrument practice";
"habit_doodle_break" = "Doodle Break";
"habit_doodle_break_desc" = "Quick creative sketching";
"habit_recipe_experiment" = "Recipe Experiment";
"habit_recipe_experiment_desc" = "Culinary creativity";
"habit_craft_project" = "Craft Project";
"habit_craft_project_desc" = "Hands-on creativity";
"habit_poetry_writing" = "Poetry Writing";
"habit_poetry_writing_desc" = "Express through verse";
"habit_color_something" = "Color Something";
"habit_color_something_desc" = "Relaxing coloring";
"habit_design_practice" = "Design Practice";
"habit_design_practice_desc" = "Visual design skills";
"habit_creative_challenge" = "Creative Challenge";
"habit_creative_challenge_desc" = "Daily creative prompt";

// MARK: - Completion Messages
"all_done" = "All Done!";
"all_habits_today" = "You've completed all your\nhabits for today";
"keep_up_great_work" = "Keep up the great work! 🎉";

// MARK: - Progress & Stats
"your_progress" = "Your Progress";
"track_habits_history" = "Track your habit streaks and history";
"no_habits_yet" = "No habits yet";
"add_first_habit_tracking" = "Add your first habit to start tracking";
"browse_templates" = "Browse Templates";
"create_new_habit" = "Create New Habit";
"this_week" = "This week";
"percent_complete" = "%d%%";
"x_of_y_completed" = "%d of %d completed";

// MARK: - Settings Extensions
"enabled" = "Enabled";
"active" = "Active";
"generic_notifications" = "Generic Notifications";
"hide_habit_names" = "Hide habit names in notifications for privacy";
"smart_notifications" = "Smart Notifications";
"reduce_notifications" = "Reduce notifications when actively using the app";
"build" = "Build";
"support" = "Support";

// MARK: - Data Management
"export_your_data" = "Export Your Data";
"export_description" = "Download all your habit data in a portable format. Your privacy is protected - all data remains on your device until you choose to share it.";
"export_format" = "Export Format";
"export_options" = "Export Options";
"date_range" = "Date Range";
"include_inactive_habits" = "Include Inactive Habits";
"export_archived_habits" = "Export data from habits you've archived";
"exported_data_contains" = "Exported files contain: habit names, completion history, creation dates, and statistics. No personal information beyond what you've entered is included.";
"data_management" = "Data Management";
"delete_all_data" = "Delete All Data";
"delete_all_warning" = "This will permanently delete all habits, logs, and settings from this device and iCloud.";
"delete_permanently_warning" = "This will permanently delete all your habits and logs. This action cannot be undone.";

// MARK: - Habit Management
"delete_selected_habits_message" = "This will permanently delete all selected habits and their data.";
"are_you_sure_delete" = "Are you sure you want to delete '%@'? This action cannot be undone.";
"habit_added" = "%@ added!";
"no_habits_yet_simple" = "No habits yet";
"start_building_routine_simple" = "Start building your routine";

// MARK: - Navigation Extensions
"reminder" = "Reminder";

// MARK: - Legend
"less_than_70" = "< 70%";
"between_70_99" = "70-99%";
"hundred_percent" = "100%";

// MARK: - Empty States
"no_templates_found" = "No templates found";
"try_different_keywords" = "Try searching for '%@' with different keywords";
"try_different_category" = "Try selecting a different category";

// MARK: - Additional ExportDataView strings
"all_time" = "All Time";
"last_month" = "Last Month";
"last_3_months" = "Last 3 Months";
"last_year" = "Last Year";
"export_data" = "Export Data";
"export_your_data" = "Export Your Data";
"export_description" = "Download all your habit data in a portable format. Your privacy is protected - all data remains on your device until you choose to share it.";
"export_format" = "Export Format";
"export_options" = "Export Options";
"date_range" = "Date Range";
"include_inactive_habits" = "Include Inactive Habits";
"export_archived_habits" = "Export data from habits you've archived";
"preparing_export" = "Preparing Export...";
"exported_data_contains" = "Exported files contain: habit names, completion history, creation dates, and statistics. No personal information beyond what you've entered is included.";
"data_management" = "Data Management";
"delete_all_data" = "Delete All Data";
"delete_all_warning" = "This will permanently delete all habits, logs, and settings from this device and iCloud.";
"delete_permanently_warning" = "This will permanently delete all your habits and logs. This action cannot be undone.";

// MARK: - ActivityView strings
"your_progress" = "Your Progress";
"track_habits_history" = "Track your habit streaks and history";
"no_habits_yet" = "No habits yet";
"add_first_habit_tracking" = "Add your first habit to start tracking";
"browse_templates" = "Browse Templates";
"create_new_habit" = "Create New Habit";
"more_habits" = "More Habits";
"habit_added" = "%@ added!";
"are_you_sure_delete" = "Are you sure you want to delete '%@'? This action cannot be undone.";
"this_week" = "This week";

// MARK: - PrivacyPolicyView strings
"privacy_policy" = "Privacy Policy";
"data_we_collect" = "Data We Collect";
"how_we_use_your_data" = "How We Use Your Data";
"data_storage_security" = "Data Storage & Security";
"your_rights_gdpr" = "Your Rights (GDPR)";
"notifications" = "Notifications";
"third_party_services" = "Third-Party Services";

// MARK: - Accessibility strings
"swipe_right_complete_left_skip" = "Swipe right to complete, left to skip, or use buttons below";
"complete_habit" = "Complete habit";
"skip_habit" = "Skip habit";
"mark_habit_complete" = "Mark this habit as completed for today";
"skip_habit_today" = "Skip this habit for today";
"progress_today" = "Progress today: %d of %d habits completed";
"undo_last_action" = "Undo last action";
"tap_to_undo" = "Tap to undo the last habit action";
"add_new_habit" = "Add new habit";
"tap_to_add_habit" = "Tap to create a new habit";
"tab_to_navigate" = "Navigate to %@ tab";

// MARK: - Error handling strings
"error" = "Error";
"ok" = "OK";
"export_failed_message" = "Failed to export data. Please try again.";
"contact_us" = "Contact Us";
"privacy_data_collect_text" = "LightningHabit collects and stores:\n• Habit names and descriptions you create\n• Daily completion records and timestamps\n• App preferences and notification settings\n• Usage analytics to improve app performance\n\nAll data is stored locally on your device and synced securely through your iCloud account.";
"privacy_data_use_text" = "Your data is used exclusively to:\n• Display your habits and track your progress\n• Send reminder notifications (if enabled)\n• Sync your data across your Apple devices\n• Calculate statistics and streaks\n\nWe never sell, share, or use your data for advertising purposes.";
"privacy_data_storage_text" = "• All habit data is encrypted and stored in your personal iCloud account\n• Local data is protected by iOS device encryption\n• No data is stored on external servers beyond Apple's iCloud\n• You maintain full control over your data at all times";
"privacy_rights_text" = "You have the right to:\n• Access all your personal data\n• Export your data in JSON or CSV format\n• Delete all your data permanently\n• Withdraw consent for notifications at any time\n• Request data portability to other apps\n\nUse the Export Data feature in Settings to download your information.";
"privacy_notifications_text" = "• Reminder notifications include your habit names\n• Notifications are processed locally on your device\n• You can disable notifications anytime in Settings\n• Notification content can be made generic in Privacy Settings";
"privacy_third_party_text" = "LightningHabit uses:\n• Apple's iCloud for data synchronization\n• Apple's notification system for reminders\n\nNo other third-party services have access to your data.";
"privacy_contact_text" = "Questions about this privacy policy or your data?\nContact us at: <EMAIL>\n\nLast updated: %@";

// MARK: - TemplateListView strings
"choose_template" = "Choose a Template";
"popular_habit_templates" = "Popular habit templates to get started";
"reminder" = "Reminder";
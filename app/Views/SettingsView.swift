//
//  SettingsView.swift
//  LightningHabit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 23..
//

import SwiftUI

struct SettingsView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var notificationManager = NotificationManager.shared
    @State private var defaultReminderTime = Date()
    @State private var skipDaysAllowed = 1
    @State private var showExportOptions = false
    @State private var showHabitManagement = false
    @State private var showPrivacyPolicy = false
    
    var body: some View {
        NavigationView {
            ZStack {
                // Clean white background
                Color.habitBackground
                    .ignoresSafeArea()
                
                ScrollView {
                    VStack(spacing: 20) {
                        // Notifications Section
                        settingsSection(title: String(localized: "notifications")) {
                            notificationSettings
                        }
                        
                        // Data Management Section
                        settingsSection(title: String(localized: "data_management")) {
                            dataManagementSettings
                        }
                        
                        // About Section
                        settingsSection(title: String(localized: "about")) {
                            aboutSettings
                        }
                    }
                    .padding()
                }
            }
            .navigationTitle(String(localized: "settings"))
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(String(localized: "done")) {
                        dismiss()
                    }
                    .foregroundColor(.habitPurple)
                    .font(.system(size: 17, weight: .medium))
                }
            }
        }
        .sheet(isPresented: $showHabitManagement) {
            HabitManagementView()
        }
        .sheet(isPresented: $showPrivacyPolicy) {
            PrivacyPolicyView()
        }
        .sheet(isPresented: $showExportOptions) {
            ExportDataView()
        }
    }
    
    // MARK: - Notification Settings
    private var notificationSettings: some View {
        VStack(spacing: 15) {
            // Notification Permission Status
            HStack {
                Label(String(localized: "notifications"), systemImage: "bell.fill")
                    .foregroundColor(.habitTextPrimary)
                
                Spacer()
                
                if notificationManager.isAuthorized {
                    Text(String(localized: "enabled"))
                        .foregroundColor(.habitSuccess)
                        .font(.caption)
                } else {
                    Button(String(localized: "enable")) {
                        Task {
                            await notificationManager.requestAuthorization()
                        }
                    }
                    .font(.caption)
                    .foregroundColor(.habitPurple)
                }
            }
            
            if notificationManager.isAuthorized {
                // Default Reminder Time
                DatePicker(
                    String(localized: "default_reminder_time"),
                    selection: $defaultReminderTime,
                    displayedComponents: .hourAndMinute
                )
                .foregroundColor(.habitTextPrimary)
                .tint(.habitPurple)
                .onChange(of: defaultReminderTime) { _, newValue in
                    notificationManager.updateDefaultTime(newValue)
                }
                
                // Privacy Settings
                Toggle(isOn: Binding(
                    get: { notificationManager.notificationSettings.useGenericContent },
                    set: { notificationManager.notificationSettings.useGenericContent = $0 }
                )) {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(String(localized: "generic_notifications"))
                            .foregroundColor(.habitTextPrimary)
                        Text(String(localized: "hide_habit_names"))
                            .font(.caption)
                            .foregroundColor(.habitTextSecondary)
                    }
                }
                .tint(.habitPurple)
                
                Toggle(isOn: Binding(
                    get: { notificationManager.notificationSettings.suppressWhenActive },
                    set: { notificationManager.notificationSettings.suppressWhenActive = $0 }
                )) {
                    VStack(alignment: .leading, spacing: 4) {
                        Text(String(localized: "smart_notifications"))
                            .foregroundColor(.habitTextPrimary)
                        Text(String(localized: "reduce_notifications"))
                            .font(.caption)
                            .foregroundColor(.habitTextSecondary)
                    }
                }
                .tint(.habitPurple)
            }
        }
    }
    
    // MARK: - Data Management Settings
    private var dataManagementSettings: some View {
        VStack(spacing: 15) {
            // iCloud Sync Status
            HStack {
                Label("iCloud Sync", systemImage: "icloud.fill")
                    .foregroundColor(.habitTextPrimary)
                
                Spacer()
                
                Text(String(localized: "active"))
                    .foregroundColor(.habitSuccess)
                    .font(.caption)
            }
            
            // Export Data Button
            Button(action: {
                showExportOptions = true
            }) {
                HStack {
                    Label(String(localized: "export_data"), systemImage: "square.and.arrow.up")
                    Spacer()
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.habitTextSecondary)
                }
                .foregroundColor(.habitTextPrimary)
            }
        }
    }
    
    // MARK: - About Settings
    private var aboutSettings: some View {
        VStack(alignment: .leading, spacing: 15) {
            HStack {
                Text(String(localized: "version"))
                    .foregroundColor(.habitTextPrimary)
                Spacer()
                Text("1.0.0")
                    .foregroundColor(.habitTextSecondary)
            }
            
            HStack {
                Text(String(localized: "build"))
                    .foregroundColor(.habitTextPrimary)
                Spacer()
                Text("1")
                    .foregroundColor(.habitTextSecondary)
            }
            
            Button(action: {
                showPrivacyPolicy = true
            }) {
                HStack {
                    Text(String(localized: "privacy_policy"))
                    Spacer()
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.habitTextSecondary)
                }
                .foregroundColor(.habitTextPrimary)
            }
            
            Button(action: {
                // TODO: Open support
            }) {
                HStack {
                    Text(String(localized: "support"))
                    Spacer()
                    Image(systemName: "chevron.right")
                        .font(.caption)
                        .foregroundColor(.habitTextSecondary)
                }
                .foregroundColor(.habitTextPrimary)
            }
        }
    }
    
    // MARK: - Helper Views
    private func settingsSection<Content: View>(title: String, @ViewBuilder content: () -> Content) -> some View {
        VStack(alignment: .leading, spacing: 15) {
            Text(title)
                .font(.headline)
                .foregroundColor(.habitTextPrimary)
            
            VStack(spacing: 15) {
                content()
            }
            .padding()
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.habitCardBackground)
                    .shadow(color: Color.black.opacity(0.05), radius: 10, x: 0, y: 4)
            )
        }
    }
}

#Preview {
    SettingsView()
}
//
//  CompletionView.swift
//  LightningHabit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 23..
//

import SwiftUI

struct CompletionView: View {
    @State private var animateIn = false
    
    var body: some View {
        ZStack {
            // Clean white card background
            RoundedRectangle(cornerRadius: 30)
                .fill(Color.habitCardBackground)
            
            VStack(spacing: 30) {
                // Checkmark animation
                ZStack {
                    Circle()
                        .fill(Color.habitPurple.opacity(0.15))
                        .frame(width: 120, height: 120)
                    
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 80))
                        .foregroundStyle(
                            LinearGradient(
                                colors: [Color.habitPurple, Color.habitPurple.opacity(0.8)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .scaleEffect(animateIn ? 1.0 : 0.5)
                        .opacity(animateIn ? 1.0 : 0.0)
                }
                
                // Congratulations text
                VStack(spacing: 12) {
                    Text(String(localized: "all_done"))
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(.habitTextPrimary)
                    
                    Text(String(localized: "all_habits_today"))
                        .font(.system(size: 18, weight: .medium))
                        .foregroundColor(.habitTextSecondary)
                        .multilineTextAlignment(.center)
                        .lineSpacing(4)
                }
                
                // Motivational message
                Text(String(localized: "keep_up_great_work"))
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.habitPurple)
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(Color.habitPurple.opacity(0.1))
                    )
                    .opacity(animateIn ? 1.0 : 0.0)
                    .offset(y: animateIn ? 0 : 20)
                    .animation(.celebration.delay(AnimationConstants.messageDelay), value: animateIn)
            }
            .padding(40)
        }
        .shadow(color: Color.black.opacity(0.2), radius: 20, x: 0, y: 10)
        .scaleEffect(animateIn ? 1.0 : 0.8)
        .opacity(animateIn ? 1.0 : 0.0)
        .onAppear {
            withAnimation(.slowPresentation) {
                animateIn = true
            }
        }
    }
}


#Preview {
    ZStack {
        Color.habitBackground
            .ignoresSafeArea()
        
        CompletionView()
            .padding()
    }
}
//
//  HabitManagementView.swift
//  LightningHabit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 23..
//

import SwiftUI
import SwiftData
import UniformTypeIdentifiers
import UserNotifications

struct HabitManagementView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    @Query(sort: \Habit.order) private var habits: [Habit]
    
    @State private var showingAddHabit = false
    @State private var editingHabit: Habit?
    @State private var showingDeleteAlert = false
    @State private var showingBulkDeleteAlert = false
    @State private var habitToDelete: Habit?
    @State private var selectedHabits = Set<Habit>()
    @State private var isSelecting = false
    @State private var draggedHabit: Habit?
    @State private var selectedTemplateCategory: TemplateCategory = .health
    
    var body: some View {
        NavigationView {
            ZStack {
                // Clean white background with subtle gradient
                LinearGradient(
                    colors: [Color.habitBackground, Color.habitBackground.opacity(0.8)],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                // Animated background circles
                GeometryReader { geometry in
                    ZStack {
                        Circle()
                            .fill(Color.habitPurple.opacity(0.05))
                            .frame(width: 300, height: 300)
                            .offset(x: -100, y: -200)
                            .blur(radius: 60)
                            .animation(.easeInOut(duration: 8).repeatForever(autoreverses: true), value: showingAddHabit)
                        
                        Circle()
                            .fill(Color.habitOrange.opacity(0.05))
                            .frame(width: 250, height: 250)
                            .offset(x: geometry.size.width - 100, y: geometry.size.height - 150)
                            .blur(radius: 50)
                            .animation(.easeInOut(duration: 10).repeatForever(autoreverses: true), value: showingAddHabit)
                    }
                }
                
                VStack(spacing: 0) {
                    // Use ScrollView only when content might overflow (more than 5 habits or has templates)
                    let needsScrolling = habits.count > 5 || (!habits.isEmpty && habits.count > 3)
                    
                    if needsScrolling {
                        ScrollView {
                            habitListContent
                        }
                        .scrollIndicators(.hidden)
                    } else {
                        habitListContent
                    }
                }
                
                // Floating action button
                VStack {
                    Spacer()
                    HStack {
                        Spacer()
                        floatingActionButton
                            .padding(.trailing, 20)
                            .padding(.bottom, 20)
                    }
                }
            }
            .navigationTitle(isSelecting ? "\(selectedHabits.count) \(String(localized: "selected"))" : String(localized: "manage_habits"))
            .navigationBarTitleDisplayMode(.large)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    if isSelecting {
                        Button(selectedHabits.count == habits.count && !habits.isEmpty ? String(localized: "deselect_all") : String(localized: "select_all")) {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                if selectedHabits.count == habits.count && !habits.isEmpty {
                                    selectedHabits.removeAll()
                                } else {
                                    selectedHabits = Set(habits)
                                }
                            }
                        }
                        .foregroundColor(.habitPurple)
                        .font(.system(size: 17))
                    } else {
                        Button(String(localized: "select")) {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                isSelecting = true
                            }
                        }
                        .foregroundColor(.habitPurple)
                        .font(.system(size: 17))
                        .disabled(habits.isEmpty)
                    }
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    if isSelecting {
                        Button(String(localized: "done")) {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                isSelecting = false
                                selectedHabits.removeAll()
                            }
                        }
                        .foregroundColor(.habitPurple)
                        .font(.system(size: 17, weight: .semibold))
                    }
                }
            }
            .sheet(isPresented: $showingAddHabit) {
                AddEditHabitView()
            }
            .sheet(item: $editingHabit) { habit in
                AddEditHabitView(habitToEdit: habit)
            }
            .alert("Delete Habit?", isPresented: $showingDeleteAlert) {
                Button(String(localized: "cancel"), role: .cancel) { }
                Button(String(localized: "delete"), role: .destructive) {
                    if let habit = habitToDelete {
                        deleteHabit(habit)
                    }
                }
            } message: {
                Text("This will permanently delete \"\(habitToDelete?.name ?? "")\" and all its data.")
            }
            .alert("Delete \(selectedHabits.count) Habits?", isPresented: $showingBulkDeleteAlert) {
                Button(String(localized: "cancel"), role: .cancel) { }
                Button(String(localized: "delete_all"), role: .destructive) {
                    deleteSelectedHabits()
                }
            } message: {
                Text("This will permanently delete all selected habits and their data.")
            }
        }
    }
    
    private var habitListContent: some View {
        LazyVStack(spacing: 12) {
            ForEach(Array(habits.enumerated()), id: \.element.id) { index, habit in
                HabitRow(
                                    habit: habit,
                                    isSelected: selectedHabits.contains(habit),
                                    isSelecting: isSelecting,
                                    onEdit: {
                                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                            editingHabit = habit
                                        }
                                    },
                                    onDelete: {
                                        habitToDelete = habit
                                        showingDeleteAlert = true
                                    },
                                    onToggleSelection: {
                                        toggleSelection(for: habit)
                                    }
                                )
                                .padding(.horizontal)
                                .onDrag {
                                    self.draggedHabit = habit
                                    return NSItemProvider(object: habit.id.uuidString as NSString)
                                }
                                .onDrop(of: [UTType.text], delegate: HabitDropDelegate(
                                    habit: habit,
                                    habits: habits,
                                    draggedHabit: $draggedHabit,
                                    onReorder: reorderHabits
                                ))
                            }
                            
                            // More Habits Section (only if user has habits)
                            if !habits.isEmpty {
                                MoreHabitsSection(
                                    selectedCategory: $selectedTemplateCategory,
                                    onAddTemplate: addHabitFromTemplate
                                )
                                .padding(.horizontal)
                                .padding(.top, 32)
                            }
                            
                            // Empty state or add button
                            if habits.isEmpty {
                                emptyStateView
                                    .padding(.top, 100)
                            }
        }
        .padding(.top, habits.isEmpty ? 0 : 16)
    }
    
    // MARK: - Views
    private var emptyStateView: some View {
        VStack(spacing: 24) {
            Image(systemName: "sparkles")
                .font(.system(size: 60))
                .foregroundStyle(LinearGradient.habitPurpleGradient)
            
            VStack(spacing: 8) {
                Text(String(localized: "no_habits_yet_simple"))
                    .font(.system(size: 24, weight: .semibold))
                    .foregroundColor(.habitTextPrimary)
                
                Text(String(localized: "start_building_routine_simple"))
                    .font(.system(size: 16))
                    .foregroundColor(.habitTextSecondary)
            }
            
            Button(action: { showingAddHabit = true }) {
                HStack(spacing: 8) {
                    Image(systemName: "plus.circle.fill")
                    Text(String(localized: "add_your_first_habit"))
                }
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.white)
                .padding(.horizontal, 24)
                .padding(.vertical, 14)
                .background(LinearGradient.habitPurpleGradient)
                .cornerRadius(30)
            }
        }
        .padding()
    }
    
    private var floatingActionButton: some View {
        Button(action: {
            impactFeedback(.light)
            showingAddHabit = true
        }) {
            Image(systemName: "plus")
                .font(.system(size: 24, weight: .semibold))
                .foregroundColor(.white)
                .frame(width: 56, height: 56)
                .background(
                    Circle()
                        .fill(LinearGradient.habitPurpleGradient)
                        .shadow(color: .habitPurple.opacity(0.3), radius: 8, x: 0, y: 4)
                )
        }
        .scaleEffect(habits.isEmpty ? 0 : 1)
        .animation(.spring(response: 0.3, dampingFraction: 0.6), value: habits.isEmpty)
    }
    
    // MARK: - Actions
    private func toggleSelection(for habit: Habit) {
        impactFeedback(.light)
        if selectedHabits.contains(habit) {
            selectedHabits.remove(habit)
        } else {
            selectedHabits.insert(habit)
        }
    }
    
    private func deleteSelectedHabits() {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            for habit in selectedHabits {
                modelContext.delete(habit)
            }
            try? modelContext.save()
            selectedHabits.removeAll()
            isSelecting = false
        }
    }
    
    private func impactFeedback(_ style: UIImpactFeedbackGenerator.FeedbackStyle) {
        let impact = UIImpactFeedbackGenerator(style: style)
        impact.impactOccurred()
    }
    
    private func addHabitFromTemplate(_ template: HabitTemplate) {
        // Check for duplicate
        let existingHabit = habits.first { habit in
            habit.name.lowercased() == template.name.lowercased()
        }
        
        if existingHabit != nil {
            // Habit already exists, provide feedback
            impactFeedback(.medium)
            return
        }
        
        let newHabit = Habit(
            name: template.name,
            icon: template.icon,
            order: habits.count
        )
        
        modelContext.insert(newHabit)
        
        // Add reminder if template has default reminder
        if template.defaultReminderEnabled, let _ = template.defaultReminderTime {
            // TODO: Schedule notification using NotificationManager
        }
        
        try? modelContext.save()
        
        // Provide haptic feedback
        let successFeedback = UINotificationFeedbackGenerator()
        successFeedback.notificationOccurred(.success)
        
        // Add animation for new habit
        withAnimation(.spring(response: 0.5, dampingFraction: 0.8)) {
            // This will trigger a re-render with the new habit
        }
    }
    
    private func deleteHabit(_ habit: Habit) {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            modelContext.delete(habit)
            try? modelContext.save()
        }
    }
    
    private func reorderHabits(_ source: Habit, targetHabit target: Habit) {
        guard source != target else { return }
        
        let sortedHabits = habits.sorted { $0.order < $1.order }
        guard let sourceIndex = sortedHabits.firstIndex(of: source),
              let targetIndex = sortedHabits.firstIndex(of: target) else { return }
        
        // Move source to target position
        var newOrder = sortedHabits
        newOrder.remove(at: sourceIndex)
        newOrder.insert(source, at: targetIndex)
        
        // Update order values
        for (index, habit) in newOrder.enumerated() {
            habit.order = index
        }
        
        try? modelContext.save()
        impactFeedback(.light)
    }
    
}

// MARK: - Habit Row
struct HabitRow: View {
    let habit: Habit
    let isSelected: Bool
    let isSelecting: Bool
    let onEdit: () -> Void
    let onDelete: () -> Void
    let onToggleSelection: () -> Void
    
    @State private var offset: CGFloat = 0
    @State private var isDragging = false
    @State private var showHintAnimation = false
    
    private var completionRate: Double {
        let logs = habit.logs
        let last30Days = logs.filter { log in
            return log.date > Date().addingTimeInterval(-30 * 24 * 60 * 60)
        }
        return last30Days.isEmpty ? 0 : Double(last30Days.filter { $0.status == .completed }.count) / Double(last30Days.count)
    }
    
    var body: some View {
        ZStack {
            // Swipe action backgrounds
            HStack {
                // Edit action
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.habitPurple)
                    .overlay(
                        Image(systemName: "pencil")
                            .font(.system(size: 24))
                            .foregroundColor(.white)
                            .padding(.leading, 24)
                        , alignment: .leading
                    )
                
                Spacer()
                
                // Delete action
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.habitError)
                    .overlay(
                        Image(systemName: "trash")
                            .font(.system(size: 24))
                            .foregroundColor(.white)
                            .padding(.trailing, 24)
                        , alignment: .trailing
                    )
            }
            
            // Main content
            HStack(spacing: 0) {
                // Selection indicator or drag handle (only show if in selecting mode)
                if isSelecting {
                    Button(action: onToggleSelection) {
                        Image(systemName: isSelected ? "checkmark.circle.fill" : "circle")
                            .font(.system(size: 24))
                            .foregroundColor(isSelected ? .habitPurple : .habitTextSecondary)
                    }
                    .padding(.leading, 16)
                    
                    // Drag handle when selecting
                    Image(systemName: "line.3.horizontal")
                        .font(.system(size: 16))
                        .foregroundColor(.habitTextTertiary)
                        .padding(.leading, 8)
                } else {
                    // No drag handle in normal mode - use spacer for alignment
                    Spacer()
                        .frame(width: 16)
                }
                
                HStack(spacing: 16) {
                    // Icon with gradient background
                    ZStack {
                        Circle()
                            .fill(LinearGradient(
                                colors: [Color.habitPurple.opacity(0.8), Color.habitPurpleDark],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ))
                            .frame(width: 56, height: 56)
                        
                        Image(systemName: habit.icon)
                            .font(.system(size: 26, weight: .medium))
                            .foregroundColor(.white)
                    }
                    
                    // Habit info with progress
                    VStack(alignment: .leading, spacing: 6) {
                        Text(habit.name)
                            .font(.system(size: 18, weight: .semibold))
                            .foregroundColor(.habitTextPrimary)
                            .lineLimit(1)
                        
                        HStack(spacing: 16) {
                            // Streak
                            HStack(spacing: 4) {
                                Image(systemName: "flame.fill")
                                    .font(.system(size: 12))
                                    .foregroundColor(.habitOrange)
                                Text("\(habit.currentStreak)")
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundColor(.habitTextSecondary)
                            }
                            
                            // Completion rate
                            HStack(spacing: 4) {
                                ProgressCircle(progress: completionRate, size: 14)
                                Text("\(Int(completionRate * 100))%")
                                    .font(.system(size: 14, weight: .medium))
                                    .foregroundColor(.habitTextSecondary)
                            }
                            
                        }
                    }
                    
                    Spacer()
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 16)
            }
            .background(
                RoundedRectangle(cornerRadius: 20)
                    .fill(Color.white)
                    .shadow(color: .habitShadow, radius: 8, x: 0, y: 2)
            )
            .offset(x: offset)
            .onTapGesture {
                if !isSelecting && !isDragging {
                    performHintAnimation()
                }
            }
            .gesture(
                DragGesture()
                    .onChanged { value in
                        withAnimation(.interactiveSpring()) {
                            offset = value.translation.width
                            isDragging = true
                        }
                    }
                    .onEnded { value in
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                            if value.translation.width > 100 {
                                // Swipe right - Edit
                                onEdit()
                                offset = 0
                            } else if value.translation.width < -100 {
                                // Swipe left - Delete
                                onDelete()
                                offset = 0
                            } else {
                                offset = 0
                            }
                            isDragging = false
                        }
                    }
            )
            .simultaneousGesture(
                LongPressGesture(minimumDuration: 0.5)
                    .onEnded { _ in
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                            if !isSelecting {
                                onToggleSelection()
                            }
                        }
                    }
            )
        }
        .scaleEffect(isSelected ? 0.98 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isSelected)
    }
    
    private func performHintAnimation() {
        // Haptic feedback
        let impactLight = UIImpactFeedbackGenerator(style: .light)
        impactLight.impactOccurred()
        
        // Animate to show both swipe actions
        withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
            offset = 50 // Show edit action
        }
        
        // After a short delay, bounce to the other side
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                offset = -50 // Show delete action
            }
            
            // After another delay, bounce back to center
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                withAnimation(.spring(response: 0.4, dampingFraction: 0.8)) {
                    offset = 0 // Return to center
                }
            }
        }
    }
}

// MARK: - Progress Circle
struct ProgressCircle: View {
    let progress: Double
    let size: CGFloat
    
    var body: some View {
        ZStack {
            Circle()
                .stroke(Color.habitTextTertiary.opacity(0.3), lineWidth: 2)
                .frame(width: size, height: size)
            
            Circle()
                .trim(from: 0, to: max(0, min(1, progress)))
                .stroke(Color.habitSuccess, lineWidth: 2)
                .frame(width: size, height: size)
                .rotationEffect(.degrees(-90))
        }
    }
}

// MARK: - Habit Drag Preview
struct HabitDragPreview: View {
    let habit: Habit
    
    var body: some View {
        HStack {
            Image(systemName: habit.icon)
                .font(.system(size: 20))
                .foregroundColor(.white)
                .frame(width: 40, height: 40)
                .background(
                    Circle()
                        .fill(LinearGradient.habitPurpleGradient)
                )
            
            Text(habit.name)
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.habitTextPrimary)
            
            Spacer()
        }
        .padding(12)
        .frame(width: 250)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .shadow(color: .habitShadow, radius: 8, x: 0, y: 4)
        )
    }
}

// MARK: - Add/Edit Habit View
struct AddEditHabitView: View {
    @Environment(\.modelContext) private var modelContext
    @Environment(\.dismiss) private var dismiss
    @Query(sort: \Habit.order) private var existingHabits: [Habit]
    
    let habitToEdit: Habit?
    
    @State private var name = ""
    @State private var selectedIcon = "star.fill"
    @State private var reminderEnabled = false
    @State private var reminderTime = Date()
    @State private var selectedCategory = "wellness"
    @State private var animateIcon = false
    @State private var showDuplicateAlert = false
    @State private var duplicateHabitName = ""
    
    private let iconCategories: [String: [String]] = [
        "wellness": ["star.fill", "heart.fill", "leaf.fill", "drop.fill", "sun.max.fill", "moon.fill"],
        "fitness": ["figure.run", "dumbbell.fill", "figure.walk", "bicycle", "sportscourt.fill", "figure.yoga"],
        "learning": ["book.fill", "brain.head.profile", "lightbulb.fill", "pencil.line", "doc.text.fill", "graduationcap.fill"],
        "productivity": ["target", "checkmark.circle.fill", "clock.fill", "calendar", "folder.fill", "briefcase.fill"],
        "health": ["pills.fill", "apple.logo", "cup.and.saucer.fill", "mouth.fill", "eye", "bed.double.fill"],
        "creative": ["paintbrush.fill", "music.note", "camera.fill", "mic.fill", "theatermasks.fill", "sparkles"]
    ]
    
    private let timePresets = [
        ("morning", DateComponents(hour: 7, minute: 0)),
        ("noon", DateComponents(hour: 12, minute: 0)),
        ("evening", DateComponents(hour: 19, minute: 0)),
        ("night", DateComponents(hour: 21, minute: 0))
    ]
    
    init(habitToEdit: Habit? = nil) {
        self.habitToEdit = habitToEdit
    }
    
    var body: some View {
        NavigationView {
            ZStack {
                // Gradient background
                LinearGradient(
                    colors: [Color.habitBackground, Color.habitSurfaceLight],
                    startPoint: .top,
                    endPoint: .bottom
                )
                .ignoresSafeArea()
                
                // Use GeometryReader to determine if content needs scrolling
                GeometryReader { geometry in
                    let contentHeight: CGFloat = 650 // Approximate content height
                    let needsScrolling = contentHeight > geometry.size.height - 100 // Account for keyboard
                    
                    if needsScrolling {
                        ScrollView {
                            addEditContent
                        }
                    } else {
                        addEditContent
                    }
                }
            }
            .navigationTitle(habitToEdit == nil ? String(localized: "new_habit") : String(localized: "edit_habit"))
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(String(localized: "cancel")) {
                        dismiss()
                    }
                    .foregroundColor(.habitPurple)
                    .font(.system(size: 17))
                }
                
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(String(localized: "save")) {
                        saveHabit()
                    }
                    .foregroundColor(.habitPurple)
                    .font(.system(size: 17, weight: .semibold))
                    .disabled(name.isEmpty)
                }
            }
            .alert("Duplicate Habit", isPresented: $showDuplicateAlert) {
                Button("OK", role: .cancel) { }
            } message: {
                Text("A habit named \"\(duplicateHabitName)\" already exists.")
            }
            .onAppear {
                if let habit = habitToEdit {
                    name = habit.name
                    selectedIcon = habit.icon
                    reminderEnabled = habit.reminderEnabled
                    if let time = habit.reminderTime {
                        reminderTime = time
                    }
                    withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                        animateIcon = true
                    }
                }
            }
        }
    }
    
    private var addEditContent: some View {
        VStack(spacing: 32) {
            // Icon preview
            iconPreview
                .padding(.top, 20)
            
            VStack(spacing: 24) {
                // Name input
                VStack(alignment: .leading, spacing: 12) {
                                Label(String(localized: "habit_name"), systemImage: "pencil.circle.fill")
                                    .font(.system(size: 16, weight: .semibold))
                                    .foregroundColor(.habitPurple)
                                
                                TextField(String(localized: "morning_meditation_placeholder"), text: $name)
                                    .font(.system(size: 18))
                                    .foregroundColor(.habitTextPrimary)
                                    .padding(16)
                                    .background(
                                        RoundedRectangle(cornerRadius: 16)
                                            .fill(Color.white)
                                            .overlay(
                                                RoundedRectangle(cornerRadius: 16)
                                                    .stroke(name.isEmpty ? Color.habitDivider : Color.habitPurple.opacity(0.3), lineWidth: 1)
                                            )
                                    )
                                    .shadow(color: .habitShadow, radius: 4, x: 0, y: 2)
                            }
                        
                // Icon selection with categories
                VStack(alignment: .leading, spacing: 16) {
                                Label(String(localized: "choose_icon"), systemImage: "square.grid.2x2.fill")
                                    .font(.system(size: 16, weight: .semibold))
                                    .foregroundColor(.habitPurple)
                                
                                // Category tabs
                                ScrollView(.horizontal, showsIndicators: false) {
                                    HStack(spacing: 12) {
                                        ForEach(iconCategories.keys.sorted(), id: \.self) { category in
                                            IconCategoryTab(
                                                title: category,
                                                isSelected: selectedCategory == category,
                                                action: {
                                                    withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                                        selectedCategory = category
                                                    }
                                                }
                                            )
                                        }
                                    }
                                }
                                
                                // Icons grid
                                LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 16), count: 4), spacing: 16) {
                                    ForEach(iconCategories[selectedCategory] ?? [], id: \.self) { icon in
                                        IconButton(
                                            icon: icon,
                                            isSelected: selectedIcon == icon,
                                            action: {
                                                withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                                    selectedIcon = icon
                                                    animateIcon = true
                                                }
                                                DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                                                    animateIcon = false
                                                }
                                            }
                                        )
                                    }
                                }
                                .padding(.horizontal, 4)
                            }
                        
                // Reminder settings
                VStack(alignment: .leading, spacing: 16) {
                    HStack {
                        Label(String(localized: "reminder_settings"), systemImage: "bell.circle.fill")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.habitPurple)
                        
                        Spacer()
                        
                        Toggle("", isOn: $reminderEnabled)
                            .tint(.habitPurple)
                    }
                    
                    if reminderEnabled {
                        // Time picker
                        DatePicker(
                            "",
                            selection: $reminderTime,
                            displayedComponents: .hourAndMinute
                        )
                        .datePickerStyle(.wheel)
                        .labelsHidden()
                        .padding(.top, 12)
                        .frame(height: 150)
                        .background(
                            RoundedRectangle(cornerRadius: 12)
                                .fill(Color.habitSurfaceLight)
                        )
                        .transition(.asymmetric(
                            insertion: .opacity.combined(with: .move(edge: .top)).animation(.spring(response: 0.4, dampingFraction: 0.8)),
                            removal: .opacity.combined(with: .move(edge: .top)).animation(.spring(response: 0.3, dampingFraction: 0.8))
                        ))
                    }
                }
                .padding(20)
                .background(
                    RoundedRectangle(cornerRadius: 20)
                        .fill(Color.white)
                        .shadow(color: .habitShadow, radius: 4, x: 0, y: 2)
                )
            }
            .padding(.horizontal)
            
            Spacer(minLength: 0)
        }
        .padding(.bottom, 32)
    }

    private func saveHabit() {
        // Check for duplicate (only for new habits or if name changed)
        if habitToEdit == nil || habitToEdit?.name != name {
            let duplicate = existingHabits.first { habit in
                habit.name.lowercased() == name.lowercased() && habit.id != habitToEdit?.id
            }
            
            if duplicate != nil {
                duplicateHabitName = name
                showDuplicateAlert = true
                return
            }
        }
        
        let habit: Habit
        
        if let habitToEdit = habitToEdit {
            // Update existing habit
            habitToEdit.name = name
            habitToEdit.icon = selectedIcon
            habitToEdit.reminderEnabled = reminderEnabled
            habitToEdit.reminderTime = reminderEnabled ? reminderTime : nil
            habit = habitToEdit
        } else {
            // Create new habit
            let newHabit = Habit(
                name: name,
                icon: selectedIcon,
                order: existingHabits.count, // Set order based on existing habits
                isActive: true,
                reminderEnabled: reminderEnabled,
                reminderTime: reminderEnabled ? reminderTime : nil
            )
            modelContext.insert(newHabit)
            habit = newHabit
        }
        
        try? modelContext.save()
        
        // Schedule notification if enabled
        if reminderEnabled {
            Task {
                await scheduleNotification(for: habit)
            }
        } else if let habitToEdit = habitToEdit, let notificationId = habitToEdit.notificationId {
            // Cancel existing notification if reminder is disabled
            Task {
                await cancelNotification(with: notificationId)
                habitToEdit.notificationId = nil
                try? modelContext.save()
            }
        }
        
        dismiss()
    }
    
    // MARK: - Notification Functions
    private func scheduleNotification(for habit: Habit) async {
        guard let reminderTime = habit.reminderTime else { return }
        
        // Request notification permission
        let center = UNUserNotificationCenter.current()
        do {
            let settings = await center.notificationSettings()
            if settings.authorizationStatus != .authorized {
                try await center.requestAuthorization(options: [.alert, .sound, .badge])
            }
            
            // Create notification content
            let content = UNMutableNotificationContent()
            content.title = "Time for \(habit.name)!"
            content.body = "Don't forget to complete your habit today 💪"
            content.sound = .default
            content.categoryIdentifier = "HABIT_REMINDER"
            
            // Create trigger - daily at the specified time
            let calendar = Calendar.current
            let components = calendar.dateComponents([.hour, .minute], from: reminderTime)
            let trigger = UNCalendarNotificationTrigger(dateMatching: components, repeats: true)
            
            // Create request
            let notificationId = "\(habit.id.uuidString)_reminder"
            let request = UNNotificationRequest(identifier: notificationId, content: content, trigger: trigger)
            
            // Cancel existing notification if any
            if let oldId = habit.notificationId {
                center.removePendingNotificationRequests(withIdentifiers: [oldId])
            }
            
            // Schedule new notification
            try await center.add(request)
            
            // Update habit with notification ID
            habit.notificationId = notificationId
            try? modelContext.save()
            
        } catch {
            print("Failed to schedule notification: \(error)")
        }
    }
    
    private func cancelNotification(with id: String) async {
        let center = UNUserNotificationCenter.current()
        center.removePendingNotificationRequests(withIdentifiers: [id])
    }
    
    // MARK: - Helper Functions
    private func isTimePresetSelected(_ components: DateComponents) -> Bool {
        let calendar = Calendar.current
        let reminderComponents = calendar.dateComponents([.hour, .minute], from: reminderTime)
        
        return reminderComponents.hour == components.hour && 
               reminderComponents.minute == components.minute
    }
    
    // MARK: - Views
    private var iconPreview: some View {
        VStack(spacing: 16) {
            ZStack {
                Circle()
                    .fill(LinearGradient.habitPurpleGradient)
                    .frame(width: 100, height: 100)
                    .shadow(color: .habitPurple.opacity(0.3), radius: 12, x: 0, y: 6)
                
                Image(systemName: selectedIcon)
                    .font(.system(size: 48, weight: .medium))
                    .foregroundColor(.white)
                    .scaleEffect(animateIcon ? 1.2 : 1.0)
                    .animation(.spring(response: 0.3, dampingFraction: 0.6), value: animateIcon)
            }
            
            if !name.isEmpty {
                Text(name)
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.habitTextPrimary)
                    .transition(.opacity.combined(with: .scale))
            }
        }
    }
}

// MARK: - Supporting Views
struct IconCategoryTab: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(LocalizedStringKey(title))
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(isSelected ? .white : .habitTextSecondary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(
                    Capsule()
                        .fill(isSelected ? AnyShapeStyle(LinearGradient.habitPurpleGradient) : AnyShapeStyle(Color.clear))
                        .overlay(
                            Capsule()
                                .stroke(isSelected ? Color.clear : Color.habitDivider, lineWidth: 1)
                        )
                )
        }
    }
}

struct IconButton: View {
    let icon: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Image(systemName: icon)
                .font(.system(size: 28, weight: .medium))
                .foregroundColor(isSelected ? .white : .habitPurple)
                .frame(width: 70, height: 70)
                .background(
                    RoundedRectangle(cornerRadius: 16)
                        .fill(isSelected ? AnyShapeStyle(LinearGradient.habitPurpleGradient) : AnyShapeStyle(Color.white))
                        .overlay(
                            RoundedRectangle(cornerRadius: 16)
                                .stroke(isSelected ? Color.clear : Color.habitDivider, lineWidth: 1)
                        )
                        .shadow(color: isSelected ? .habitPurple.opacity(0.3) : .habitShadow, 
                               radius: isSelected ? 8 : 4, 
                               x: 0, y: isSelected ? 4 : 2)
                )
                .scaleEffect(isSelected ? 1.05 : 1.0)
        }
    }
}

struct TimePresetButton: View {
    let title: String
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(LocalizedStringKey(title))
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(isSelected ? .white : .habitTextPrimary)
                .padding(.horizontal, 20)
                .padding(.vertical, 10)
                .background(
                    RoundedRectangle(cornerRadius: 12)
                        .fill(isSelected ? AnyShapeStyle(LinearGradient.habitPurpleGradient) : AnyShapeStyle(Color.habitSurfaceLight))
                )
        }
    }
}

// MARK: - More Habits Components

struct HabitTemplateCard: View {
    let template: HabitTemplate
    let onAdd: () -> Void
    let isAlreadyAdded: Bool
    
    @State private var isPressed = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header with icon and add button
            HStack {
                // Template icon
                Image(systemName: template.icon)
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.habitPurple.opacity(0.8))
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(Color.habitPurple.opacity(0.1))
                    )
                
                Spacer()
                
                // Add button or checkmark if already added
                if isAlreadyAdded {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 28))
                        .foregroundColor(.habitSuccess)
                        .background(
                            Circle()
                                .fill(Color.white)
                                .frame(width: 24, height: 24)
                        )
                } else {
                    Button(action: {
                        impactFeedback(.light)
                        onAdd()
                    }) {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 28))
                            .foregroundColor(.habitPurple)
                            .background(
                                Circle()
                                    .fill(Color.white)
                                    .frame(width: 24, height: 24)
                            )
                    }
                    .scaleEffect(isPressed ? 0.9 : 1.0)
                    .animation(.spring(response: 0.2, dampingFraction: 0.6), value: isPressed)
                    .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
                        isPressed = pressing
                    }, perform: {})
                }
            }
            
            // Template info
            VStack(alignment: .leading, spacing: 4) {
                Text(template.name)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.habitTextPrimary)
                    .lineLimit(1)
                
                Text(template.description)
                    .font(.system(size: 13))
                    .foregroundColor(.habitTextSecondary)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
            }
            
            // Template badge
            HStack {
                Text(String(localized: "template"))
                    .font(.system(size: 10, weight: .bold))
                    .foregroundColor(.habitPurple.opacity(0.7))
                    .padding(.horizontal, 8)
                    .padding(.vertical, 2)
                    .background(
                        Capsule()
                            .fill(Color.habitPurple.opacity(0.1))
                    )
                
                Spacer()
                
                if template.defaultReminderEnabled {
                    Image(systemName: "bell.fill")
                        .font(.system(size: 10))
                        .foregroundColor(.habitOrange.opacity(0.8))
                }
            }
        }
        .padding(16)
        .frame(width: 200, height: 140)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(isAlreadyAdded ? Color.habitSurfaceLight : Color.white)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(isAlreadyAdded ? Color.habitSuccess.opacity(0.3) : Color.habitPurple.opacity(0.2), lineWidth: 1)
                )
                .shadow(color: .habitShadow, radius: isAlreadyAdded ? 2 : 4, x: 0, y: isAlreadyAdded ? 1 : 2)
        )
        .opacity(isAlreadyAdded ? 0.7 : 1.0)
    }
    
    private func impactFeedback(_ style: UIImpactFeedbackGenerator.FeedbackStyle) {
        let impact = UIImpactFeedbackGenerator(style: style)
        impact.impactOccurred()
    }
}

struct MoreHabitsSection: View {
    @Binding var selectedCategory: TemplateCategory
    let onAddTemplate: (HabitTemplate) -> Void
    @Query(sort: \Habit.order) private var existingHabits: [Habit]
    @State private var showingFullTemplateView = false
    
    private var templatesForCategory: [HabitTemplate] {
        HabitTemplate.templates(for: selectedCategory)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Section header with browse button
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(String(localized: "more_habits"))
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.habitTextPrimary)
                    
                    Text(String(localized: "popular_habit_templates"))
                        .font(.system(size: 14))
                        .foregroundColor(.habitTextSecondary)
                }
                
                Spacer()
                
                Button(action: {
                    showingFullTemplateView = true
                }) {
                    Text(String(localized: "browse_templates"))
                        .font(.system(size: 14, weight: .semibold))
                        .foregroundColor(.habitPurple)
                }
            }
            .padding(.horizontal, 16)
            
            // Category tabs
            CategoryTabBar(selectedCategory: $selectedCategory)
            
            // Template cards
            ScrollView(.horizontal, showsIndicators: false) {
                LazyHStack(spacing: 24) {
                    ForEach(Array(templatesForCategory.enumerated()), id: \.element.id) { index, template in
                        let isAlreadyAdded = existingHabits.contains { habit in
                            habit.name.lowercased() == template.name.lowercased()
                        }
                        
                        HabitTemplateCard(
                            template: template,
                            onAdd: {
                                onAddTemplate(template)
                            },
                            isAlreadyAdded: isAlreadyAdded
                        )
                        .transition(.asymmetric(
                            insertion: .scale.combined(with: .opacity).animation(.spring(response: 0.4, dampingFraction: 0.8).delay(Double(index) * 0.05)),
                            removal: .scale.combined(with: .opacity)
                        ))
                    }
                }
                .padding(.horizontal, 20)
            }
            .animation(.spring(response: 0.4, dampingFraction: 0.8), value: selectedCategory)
        }
        .padding(.vertical, 16)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.habitBackground.opacity(0.3))
                .overlay(
                    RoundedRectangle(cornerRadius: 20)
                        .stroke(Color.habitDivider.opacity(0.5), lineWidth: 1)
                )
        )
        .sheet(isPresented: $showingFullTemplateView) {
            NavigationView {
                TemplateListView(
                    selectedCategory: $selectedCategory,
                    onAddTemplate: onAddTemplate,
                    showCloseButton: true,
                    onClose: {
                        showingFullTemplateView = false
                    }
                )
                .padding()
                .navigationTitle(String(localized: "choose_template"))
                .navigationBarTitleDisplayMode(.inline)
                .toolbar {
                    ToolbarItem(placement: .navigationBarTrailing) {
                        Button(String(localized: "done")) {
                            showingFullTemplateView = false
                        }
                        .foregroundColor(.habitPurple)
                    }
                }
            }
        }
    }
}

// MARK: - Drop Delegate
struct HabitDropDelegate: DropDelegate {
    let habit: Habit
    let habits: [Habit]
    @Binding var draggedHabit: Habit?
    let onReorder: (Habit, Habit) -> Void
    
    func dropEntered(info: DropInfo) {
        guard let draggedHabit = draggedHabit,
              draggedHabit != habit else { return }
        
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            onReorder(draggedHabit, habit)
        }
    }
    
    func dropUpdated(info: DropInfo) -> DropProposal? {
        return DropProposal(operation: .move)
    }
    
    func performDrop(info: DropInfo) -> Bool {
        self.draggedHabit = nil
        return true
    }
}


#Preview {
    HabitManagementView()
}

//
//  HabitProgressCard.swift
//  LightningHabit
//
//  Swipeable habit card for activity view
//

import SwiftUI

struct HabitProgressCard: View {
    let habit: Habit
    let onEdit: () -> Void
    let onDelete: () -> Void
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            // Header
            HStack {
                // Icon
                Image(systemName: habit.icon)
                    .font(.system(size: 24))
                    .foregroundColor(.habitPurple)
                    .frame(width: 44, height: 44)
                    .background(
                        Circle()
                            .fill(Color.habitPurple.opacity(0.1))
                    )
                
                // Name and streak
                VStack(alignment: .leading, spacing: 4) {
                    Text(habit.name)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.habitTextPrimary)
                    
                    HStack(spacing: 12) {
                        Label("\(habit.currentStreak) days", systemImage: "flame.fill")
                            .font(.system(size: 14, weight: .medium))
                            .foregroundColor(habit.currentStreak > 0 ? .orange : .habitTextSecondary)
                        
                        if habit.longestStreak > habit.currentStreak {
                            Label("Best: \(habit.longestStreak)", systemImage: "trophy.fill")
                                .font(.system(size: 14))
                                .foregroundColor(.habitTextSecondary)
                        }
                    }
                }
                
                Spacer()
                
                // Completion rate
                VStack(alignment: .trailing, spacing: 4) {
                    Text("\(Int(max(0, min(1, habit.completionRate)) * 100))%")
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(.habitTextPrimary)
                    
                    Text(String(localized: "this_week"))
                        .font(.system(size: 12))
                        .foregroundColor(.habitTextSecondary)
                }
            }
            
            // Week progress
            HStack(spacing: 8) {
                ForEach(0..<7) { dayOffset in
                    let date = Calendar.current.date(byAdding: .day, value: dayOffset - 6, to: Date())!
                    let isCompleted = habit.logs.contains { log in
                        Calendar.current.isDate(log.date, inSameDayAs: date) && log.status == .completed
                    }
                    let isToday = Calendar.current.isDateInToday(date)
                    
                    VStack(spacing: 4) {
                        Text(dayLabel(for: date))
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(.habitTextSecondary)
                        
                        Circle()
                            .fill(
                                isCompleted 
                                ? Color.habitPurple
                                : Color.habitCardBackground
                            )
                            .frame(width: 32, height: 32)
                            .overlay(
                                Circle()
                                    .stroke(
                                        isToday ? Color.habitPurple : Color.habitTextSecondary.opacity(0.2),
                                        lineWidth: isToday ? 2 : 1
                                    )
                            )
                            .overlay(
                                Image(systemName: "checkmark")
                                    .font(.system(size: 14, weight: .bold))
                                    .foregroundColor(.white)
                                    .opacity(isCompleted ? 1 : 0)
                            )
                    }
                }
            }
        }
        .padding(20)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.habitCardBackground)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
        .frame(height: 140)
    }
    
    private func dayLabel(for date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "E"
        return String(formatter.string(from: date).prefix(1))
    }
}
//
//  WeeklyChart.swift
//  LightningHabit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 23..
//

import SwiftUI

struct WeeklyChart: View {
    let data: [DayData]
    
    struct DayData {
        let day: String
        let completed: Int
        let total: Int
        
        var percentage: Double {
            guard total > 0 else { return 0 }
            return Double(completed) / Double(total)
        }
    }
    
    var body: some View {
        VStack(spacing: 16) {
            HStack(alignment: .bottom, spacing: 12) {
                ForEach(data, id: \.day) { dayData in
                    VStack(spacing: 8) {
                        // Bar
                        GeometryReader { geometry in
                            VStack {
                                Spacer()
                                
                                // Completed portion with background
                                ZStack(alignment: .bottom) {
                                    // Background
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(Color.habitPurple.opacity(0.1))
                                        .frame(height: geometry.size.height)
                                    
                                    // Completed portion
                                    RoundedRectangle(cornerRadius: 8)
                                        .fill(
                                            dayData.percentage >= 1.0 
                                            ? LinearGradient(
                                                colors: [Color.habitSuccess, Color.habitSuccess.opacity(0.8)],
                                                startPoint: .top,
                                                endPoint: .bottom
                                            )
                                            : dayData.percentage >= 0.7 
                                            ? LinearGradient.habitPurpleGradient
                                            : LinearGradient(
                                                colors: [Color.habitOrange, Color.habitOrange.opacity(0.8)],
                                                startPoint: .top,
                                                endPoint: .bottom
                                            )
                                        )
                                        .frame(height: max(8, max(0, geometry.size.height) * max(0, min(1, dayData.percentage))))
                                }
                            }
                        }
                        .frame(height: 120)
                        
                        // Day label
                        VStack(spacing: 2) {
                            Text(dayData.day)
                                .font(.system(size: 12, weight: .medium))
                                .foregroundColor(.habitTextSecondary)
                            
                            // Show completion info
                            if dayData.total > 0 {
                                Text("\(dayData.completed)/\(dayData.total)")
                                    .font(.system(size: 10, weight: .medium))
                                    .foregroundColor(.habitTextTertiary)
                            }
                        }
                    }
                }
            }
            
            // Legend
            HStack(spacing: 20) {
                HStack(spacing: 8) {
                    Circle()
                        .fill(Color.habitOrange)
                        .frame(width: 8, height: 8)
                    Text(String(localized: "less_than_70"))
                        .font(.system(size: 12))
                        .foregroundColor(.habitTextSecondary)
                }
                
                HStack(spacing: 8) {
                    Circle()
                        .fill(Color.habitPurple)
                        .frame(width: 8, height: 8)
                    Text(String(localized: "between_70_99"))
                        .font(.system(size: 12))
                        .foregroundColor(.habitTextSecondary)
                }
                
                HStack(spacing: 8) {
                    Circle()
                        .fill(Color.habitSuccess)
                        .frame(width: 8, height: 8)
                    Text(String(localized: "hundred_percent"))
                        .font(.system(size: 12))
                        .foregroundColor(.habitTextSecondary)
                }
            }
        }
    }
}

struct MonthlyChart: View {
    let data: [WeeklyChart.DayData]
    
    var body: some View {
        CalendarMonthView()
    }
}

struct CircularProgress: View {
    let progress: Double
    let color: Color
    let lineWidth: CGFloat = 8
    
    var body: some View {
        ZStack {
            // Background circle
            Circle()
                .stroke(Color.gray.opacity(0.1), lineWidth: lineWidth)
            
            // Progress arc
            Circle()
                .trim(from: 0, to: max(0, min(1, progress)))
                .stroke(
                    LinearGradient(
                        colors: [color, color.opacity(0.7)],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    ),
                    style: StrokeStyle(lineWidth: lineWidth, lineCap: .round)
                )
                .rotationEffect(.degrees(-90))
                .animation(.spring(response: 0.4, dampingFraction: 0.8), value: progress)
        }
    }
}
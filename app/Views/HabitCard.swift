//
//  HabitCard.swift
//  LightningHabit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 23..
//

import SwiftUI

struct HabitCard: View {
    let habit: Habit
    @Binding var dragOffset: CGSize
    @Binding var isDragging: Bool
    let onComplete: () -> Void
    let onSkip: () -> Void
    
    @State private var showHint = UserDefaults.standard.bool(forKey: "hasCompletedFirstSwipe") ? false : true
    @State private var animateHint = false
    
    // Constants
    private let swipeThreshold: CGFloat = 100
    private let rotationMultiplier: Double = 0.2
    
    // Computed properties
    private var dragPercentage: CGFloat {
        min(abs(dragOffset.width) / swipeThreshold, 1.0)
    }
    
    private var rotationAngle: Double {
        Double(dragOffset.width) * rotationMultiplier / 10
    }
    
    private var swipeDirection: SwipeDirection {
        if dragOffset.width > swipeThreshold {
            return .right
        } else if dragOffset.width < -swipeThreshold {
            return .left
        }
        return .none
    }
    
    private var overlayOpacity: Double {
        switch swipeDirection {
        case .right, .left:
            return Double(dragPercentage) * 0.8
        case .none:
            return 0
        }
    }
    
    var body: some View {
        ZStack {
            // Card background with full purple gradient
            cardBackground
            
            // Card content
            VStack(spacing: 16) {
                Spacer()
                
                // Large habit.icon
                Image(systemName: habit.icon)
                    .font(.system(size: 120, weight: .medium))
                    .foregroundColor(.white)
                    .frame(height: 216) // 45% of 480px height
                
                // Habit name
                Text(habit.name)
                    .font(.system(size: 32, weight: .bold))
                    .foregroundColor(.white)
                    .multilineTextAlignment(.center)
                    .lineLimit(2)
                    .padding(.horizontal, 30)
                
                // Time or description
                Text(getTimeDescription())
                    .font(.system(size: 18, weight: .medium))
                    .foregroundColor(.white.opacity(0.8))
                
                // Streak indicator
                if habit.currentStreak > 0 {
                    HStack(spacing: 8) {
                        Image(systemName: "flame.fill")
                            .font(.system(size: 22))
                            .foregroundColor(.white)
                        
                        Text(habit.currentStreak == 1 ? "day_streak".localized(with: habit.currentStreak) : "days_streak".localized(with: habit.currentStreak))
                            .font(.system(size: 20, weight: .semibold))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, 24)
                    .padding(.vertical, 12)
                    .background(
                        RoundedRectangle(cornerRadius: 20)
                            .fill(Color.white.opacity(0.2))
                            .overlay(
                                RoundedRectangle(cornerRadius: 20)
                                    .stroke(Color.white.opacity(0.3), lineWidth: 1)
                            )
                    )
                }
                
                Spacer()
                
                // Action buttons
                HStack(spacing: 50) {
                    ActionButton(
                        icon: "xmark",
                        color: .habitError,
                        action: handleSkip,
                        isOnPurple: true
                    )
                    
                    ActionButton(
                        icon: "checkmark",
                        color: .habitSuccess,
                        action: handleComplete,
                        isOnPurple: true
                    )
                }
                .padding(.bottom, 40)
            }
            .padding(.horizontal, 20)
            
            // Swipe overlay indicators
            if isDragging {
                swipeOverlay
            }
            
            // First-time user hint
            if showHint {
                swipeHintOverlay
            }
        }
        .frame(maxWidth: .infinity)
        .frame(height: 480)
        .offset(dragOffset)
        .rotationEffect(.degrees(rotationAngle))
        .scaleEffect(isDragging ? 0.98 : 1.0)
        .animation(.standardUI, value: isDragging)
        .gesture(
            DragGesture()
                .onChanged { value in
                    isDragging = true
                    dragOffset = value.translation
                    
                    // Haptic feedback at threshold
                    if abs(value.translation.width) > swipeThreshold && abs(value.translation.width) < swipeThreshold + 5 {
                        let impact = UIImpactFeedbackGenerator(style: .medium)
                        impact.impactOccurred()
                    }
                }
                .onEnded { value in
                    isDragging = false
                    
                    if value.translation.width > swipeThreshold {
                        // Swipe right - Complete
                        animateCardOffScreen(direction: .right) {
                            handleComplete()
                        }
                    } else if value.translation.width < -swipeThreshold {
                        // Swipe left - Skip
                        animateCardOffScreen(direction: .left) {
                            handleSkip()
                        }
                    } else {
                        // Spring back
                        withAnimation(.standardUI) {
                            dragOffset = .zero
                        }
                    }
                }
        )
        // MARK: - Accessibility
        .accessibilityElement(children: .combine)
        .accessibilityLabel("\(habit.name) habit")
        .accessibilityHint("swipe_right_complete_left_skip".localized)
        .accessibilityAddTraits(.allowsDirectInteraction)
        .accessibilityAction(named: "complete_habit".localized) {
            handleComplete()
        }
        .accessibilityAction(named: "skip_habit".localized) {
            handleSkip()
        }

    }
    
    // MARK: - Card Background
    private var cardBackground: some View {
        RoundedRectangle(cornerRadius: 30)
            .fill(LinearGradient.habitPurpleGradient)
            .shadow(color: Color.habitPurple.opacity(0.3), radius: 20, x: 0, y: 10)
    }
    
    // MARK: - Swipe Overlay
    private var swipeOverlay: some View {
        ZStack {
            if swipeDirection == .right {
                // Complete overlay - Green for DONE
                RoundedRectangle(cornerRadius: 30)
                    .fill(Color.habitSuccess.opacity(overlayOpacity * 0.3))
                    .overlay(
                        RoundedRectangle(cornerRadius: 30)
                            .stroke(Color.habitSuccess.opacity(overlayOpacity * 0.8), lineWidth: 3)
                    )
                    .overlay(
                        VStack(spacing: 12) {
                            Image(systemName: "checkmark.circle.fill")
                                .font(.system(size: 80))
                                .foregroundColor(.habitSuccess)
                            Text("done".localized.uppercased())
                                .font(.system(size: 28, weight: .bold))
                                .foregroundColor(.habitSuccess)
                        }
                        .opacity(overlayOpacity)
                    )
            } else if swipeDirection == .left {
                // Skip overlay - Red for SKIP
                RoundedRectangle(cornerRadius: 30)
                    .fill(Color.habitError.opacity(overlayOpacity * 0.3))
                    .overlay(
                        RoundedRectangle(cornerRadius: 30)
                            .stroke(Color.habitError.opacity(overlayOpacity * 0.8), lineWidth: 3)
                    )
                    .overlay(
                        VStack(spacing: 12) {
                            Image(systemName: "xmark.circle.fill")
                                .font(.system(size: 80))
                                .foregroundColor(.habitError)
                            Text("skip".localized.uppercased())
                                .font(.system(size: 28, weight: .bold))
                                .foregroundColor(.habitError)
                        }
                        .opacity(overlayOpacity)
                    )
            }
        }
    }
    
    // MARK: - Swipe Hint Overlay
    private var swipeHintOverlay: some View {
        HStack {
            // Left hint
            VStack(spacing: 8) {
                Image(systemName: "arrow.left.circle.fill")
                    .font(.system(size: 36))
                    .foregroundColor(.habitError)
                Text("skip".localized)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.habitTextSecondary)
            }
            .offset(x: animateHint ? -10 : 0)
            
            Spacer()
            
            // Right hint
            VStack(spacing: 8) {
                Image(systemName: "arrow.right.circle.fill")
                    .font(.system(size: 36))
                    .foregroundColor(.habitSuccess)
                Text("done".localized)
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.habitTextSecondary)
            }
            .offset(x: animateHint ? 10 : 0)
        }
        .padding(.horizontal, 40)
        .opacity(0.8)
        .onAppear {
            withAnimation(.longBackground.repeatForever(autoreverses: true)) {
                animateHint = true
            }
        }
    }
    
    // MARK: - Actions
    private func handleComplete() {
        let impact = UIImpactFeedbackGenerator(style: .heavy)
        impact.impactOccurred()
        
        // Mark that user has completed first swipe
        if showHint {
            UserDefaults.standard.set(true, forKey: "hasCompletedFirstSwipe")
            showHint = false
        }
        
        onComplete()
        resetCard()
    }
    
    private func handleSkip() {
        let impact = UIImpactFeedbackGenerator(style: .medium)
        impact.impactOccurred()
        
        // Mark that user has completed first swipe
        if showHint {
            UserDefaults.standard.set(true, forKey: "hasCompletedFirstSwipe")
            showHint = false
        }
        
        onSkip()
        resetCard()
    }
    
    private func animateCardOffScreen(direction: SwipeDirection, completion: @escaping () -> Void) {
        withAnimation(.quickInteraction) {
            switch direction {
            case .right:
                dragOffset = CGSize(width: UIScreen.main.bounds.width + 200, height: 0)
            case .left:
                dragOffset = CGSize(width: -UIScreen.main.bounds.width - 200, height: 0)
            case .none:
                break
            }
        }
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            completion()
        }
    }
    
    private func resetCard() {
        dragOffset = .zero
    }
    
    private func getStreakMessage(_ streak: Int) -> String {
        switch streak {
        case 7..<14:
            return "1 week strong! 💪"
        case 14..<30:
            return "2 weeks! Amazing! 🔥"
        case 30..<60:
            return "1 month! Incredible! 🌟"
        case 60..<90:
            return "2 months! Unstoppable! 🚀"
        case 90...:
            return "3+ months! Legend! 👑"
        default:
            return "Keep it up!"
        }
    }
    
    private func getTimeDescription() -> String {
        if habit.reminderEnabled, let reminderTime = habit.reminderTime {
            let formatter = DateFormatter()
            formatter.timeStyle = .short
            
            let calendar = Calendar.current
            let now = Date()
            let reminderHour = calendar.component(.hour, from: reminderTime)
            let reminderMinute = calendar.component(.minute, from: reminderTime)
            let currentHour = calendar.component(.hour, from: now)
            let currentMinute = calendar.component(.minute, from: now)
            
            // Check if reminder is within the next hour
            let reminderMinutes = reminderHour * 60 + reminderMinute
            let currentMinutes = currentHour * 60 + currentMinute
            let diff = (reminderMinutes - currentMinutes + 1440) % 1440
            
            if diff > 0 && diff <= 60 {
                return "\(formatter.string(from: reminderTime)) • Due soon"
            } else {
                return formatter.string(from: reminderTime)
            }
        } else {
            // Fallback to generic time description
            let hour = Calendar.current.component(.hour, from: Date())
            switch hour {
            case 5..<9:
                return "morning_habit".localized
            case 9..<12:
                return "mid_morning".localized
            case 12..<14:
                return "lunch_time".localized
            case 14..<17:
                return "afternoon".localized
            case 17..<20:
                return "evening".localized
            case 20..<23:
                return "night_routine".localized
            default:
                return "daily_habit".localized
            }
        }
    }
}

// MARK: - Supporting Types
enum SwipeDirection {
    case left, right, none
}

struct ActionButton: View {
    let icon: String
    let color: Color
    let action: () -> Void
    var isOnPurple: Bool = false
    
    @State private var isPressed = false
    
    var body: some View {
        Button(action: {
            withAnimation(.quickInteraction) {
                action()
            }
        }) {
            ZStack {
                // White background with shadow
                Circle()
                    .fill(Color.white)
                    .frame(width: 68, height: 68)
                    .shadow(color: isOnPurple ? Color.black.opacity(0.15) : .habitShadow, radius: 8, x: 0, y: 4)
                
                // Icon
                Image(systemName: icon)
                    .font(.system(size: 30, weight: .semibold))
                    .foregroundColor(color)
            }
            .scaleEffect(isPressed ? 0.9 : 1.0)
        }
        .buttonStyle(PlainButtonStyle())
        .accessibilityLabel(icon == "checkmark" ? "complete_habit".localized : "skip_habit".localized)
        .accessibilityHint(icon == "checkmark" ? "mark_habit_complete".localized : "skip_habit_today".localized)
        .accessibilityAddTraits(.isButton)
        .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
            withAnimation(.quickInteraction) {
                isPressed = pressing
            }
        }, perform: {})
    }
}

#Preview {
    ZStack {
        Color.habitBackground
            .ignoresSafeArea()
        
        let sampleHabit = Habit(
            name: "Morning Meditation",
            icon: "brain.head.profile",
            reminderEnabled: true,
            reminderTime: Date()
        )
        
        HabitCard(
            habit: sampleHabit,
            dragOffset: .constant(.zero),
            isDragging: .constant(false),
            onComplete: { },
            onSkip: { }
        )
        .padding()
    }
}
//
//  ActivityView.swift
//  LightningHabit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 23..
//

import SwiftUI
import SwiftData

struct ActivityView: View {
    @Environment(\.modelContext) private var modelContext
    @Query(
        filter: #Predicate<Habit> { $0.isActive },
        sort: \Habit.order
    ) private var habits: [Habit]
    
    @Binding var showHabitManagement: Bool
    
    @State private var showAddedAnimation = false
    @State private var addedHabitName = ""
    @State private var selectedCategory: TemplateCategory = .health
    @State private var showTemplates = false
    @State private var hasAppeared = false
    @State private var editingHabit: Habit?
    @State private var showDeleteConfirmation = false
    @State private var habitToDelete: Habit?
    @State private var showAddHabit = false
    
    private var habitStats: [Habit] {
        habits.sorted { $0.order < $1.order }
    }
    
    var body: some View {
        NavigationView {
            if habits.isEmpty {
                // Empty state
                ScrollView {
                    VStack(spacing: 20) {
                        // Header
                        VStack(alignment: .leading, spacing: 8) {
                            Text(String(localized: "your_progress"))
                                .font(.system(size: 28, weight: .bold))
                                .foregroundColor(.habitTextPrimary)
                            
                            Text(String(localized: "track_habits_history"))
                                .font(.system(size: 16))
                                .foregroundColor(.habitTextSecondary)
                        }
                        .frame(maxWidth: .infinity, alignment: .leading)
                        .padding(.horizontal)
                        .padding(.top, 12)
                        .offset(y: hasAppeared ? 0 : -20)
                        .animation(.slowPresentation.delay(AnimationConstants.baseDelay), value: hasAppeared)
                        
                        Spacer(minLength: 80)
                        
                        Image(systemName: "chart.line.uptrend.xyaxis")
                            .font(.system(size: 60))
                            .foregroundColor(.habitTextSecondary.opacity(0.3))
                        
                        VStack(spacing: 8) {
                            Text(String(localized: "no_habits_yet"))
                                .font(.system(size: 20, weight: .semibold))
                                .foregroundColor(.habitTextPrimary)
                            
                            Text(String(localized: "add_first_habit_tracking"))
                                .font(.system(size: 16))
                                .foregroundColor(.habitTextSecondary)
                                .multilineTextAlignment(.center)
                        }
                        
                        Button(action: {
                            showAddHabit = true
                        }) {
                            Text(String(localized: "create_new_habit"))
                                .font(.system(size: 16, weight: .medium))
                                .foregroundColor(.white)
                                .padding(.horizontal, 24)
                                .padding(.vertical, 12)
                                .background(
                                    LinearGradient.habitPurpleGradient
                                )
                                .cornerRadius(12)
                        }
                        
                        Spacer(minLength: 80)
                    }
                    .frame(maxWidth: .infinity)
                    .padding(.horizontal)
                    .scaleEffect(hasAppeared ? 1 : 0.95)
                    .animation(.slowPresentation.delay(AnimationConstants.baseDelay * 2), value: hasAppeared)
                }
                .navigationBarHidden(true)
                .background(Color.habitBackground)
            } else {
                // List with habit cards
                List {
                    // Header section
                    VStack(alignment: .leading, spacing: 8) {
                        Text(String(localized: "your_progress"))
                            .font(.system(size: 28, weight: .bold))
                            .foregroundColor(.habitTextPrimary)
                        
                        Text(String(localized: "track_habits_history"))
                            .font(.system(size: 16))
                            .foregroundColor(.habitTextSecondary)
                    }
                    .listRowBackground(Color.clear)
                    .listRowSeparator(.hidden)
                    .listRowInsets(EdgeInsets(top: 12, leading: 16, bottom: 8, trailing: 16))
                    .offset(y: hasAppeared ? 0 : -20)
                    .animation(.slowPresentation.delay(AnimationConstants.baseDelay), value: hasAppeared)
                    
                    // Habit cards with swipe actions
                    ForEach(Array(habitStats.enumerated()), id: \.element.id) { index, habit in
                        HabitProgressCard(
                            habit: habit,
                            onEdit: {
                                editingHabit = habit
                            },
                            onDelete: {
                                habitToDelete = habit
                                showDeleteConfirmation = true
                            }
                        )
                        .listRowBackground(Color.clear)
                        .listRowSeparator(.hidden)
                        .listRowInsets(EdgeInsets(top: 8, leading: 16, bottom: 8, trailing: 16))
                        .swipeActions(edge: .trailing, allowsFullSwipe: false) {
                            Button(role: .destructive) {
                                habitToDelete = habit
                                showDeleteConfirmation = true
                            } label: {
                                Label(String(localized: "delete"), systemImage: "trash")
                            }
                            .tint(.red)
                        }
                        .swipeActions(edge: .leading, allowsFullSwipe: false) {
                            Button {
                                editingHabit = habit
                            } label: {
                                Label(String(localized: "edit"), systemImage: "pencil")
                            }
                            .tint(.habitPurple)
                        }
                    }
                    
                    // Removed Browse Templates button from Activity View
                    
                    // Bottom spacing for navigation bar
                    Color.clear
                        .frame(height: 100)
                        .listRowBackground(Color.clear)
                        .listRowSeparator(.hidden)
                        .listRowInsets(EdgeInsets())
                }
                .listStyle(.plain)
                .scrollContentBackground(.hidden)
                .background(Color.habitBackground)
                .navigationBarHidden(true)
            }
        }
        .onAppear {
            hasAppeared = true
        }
        .onDisappear {
            hasAppeared = false
        }
        .overlay {
            // Success animation overlay
            if showAddedAnimation {
                VStack {
                    Spacer()
                    
                    HStack(spacing: 12) {
                        Image(systemName: "checkmark.circle.fill")
                            .font(.system(size: 24))
                            .foregroundColor(.white)
                        
                        Text(String(localized: "habit_added", defaultValue: "%@ added!", table: nil).replacingOccurrences(of: "%@", with: addedHabitName))
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.white)
                    }
                    .padding(.horizontal, 20)
                    .padding(.vertical, 16)
                    .background(
                        RoundedRectangle(cornerRadius: 12)
                            .fill(Color.habitSuccess)
                            .shadow(color: Color.habitSuccess.opacity(0.3), radius: 8)
                    )
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                    .padding(.bottom, 120)
                }
                .animation(.quickInteraction, value: showAddedAnimation)
            }
        }
        .sheet(isPresented: $showTemplates) {
            NavigationView {
                TemplateListView(
                    selectedCategory: $selectedCategory,
                    onAddTemplate: { template in
                        addHabitFromTemplate(template)
                        showTemplates = false
                    },
                    showCloseButton: true,
                    onClose: {
                        showTemplates = false
                    }
                )
                .padding()
                .navigationBarHidden(true)
                .background(Color.habitBackground)
            }
            .presentationDetents([.large])
            .presentationDragIndicator(.visible)
        }
        .sheet(item: $editingHabit) { habit in
            AddEditHabitView(habitToEdit: habit)
        }
        .sheet(isPresented: $showAddHabit) {
            AddEditHabitView()
        }
        .alert(String(localized: "delete_habit_question"), isPresented: $showDeleteConfirmation) {
            Button(String(localized: "cancel"), role: .cancel) {
                habitToDelete = nil
            }
            Button(String(localized: "delete"), role: .destructive) {
                if let habit = habitToDelete {
                    deleteHabit(habit)
                }
                habitToDelete = nil
            }
        } message: {
            if let habit = habitToDelete {
                Text(String(localized: "are_you_sure_delete", defaultValue: "Are you sure you want to delete '%@'? This action cannot be undone.", table: nil).replacingOccurrences(of: "%@", with: habit.name))
            }
        }
    }
    
    private func addHabitFromTemplate(_ template: HabitTemplate) {
        withAnimation(.quickInteraction) {
            let newHabit = Habit(
                name: template.name,
                icon: template.icon,
                reminderEnabled: template.defaultReminderEnabled,
                reminderTime: template.defaultReminderTime
            )
            
            newHabit.order = habits.count
            modelContext.insert(newHabit)
            
            do {
                try modelContext.save()
                
                // Show success animation
                addedHabitName = template.name
                showAddedAnimation = true
                
                // Haptic feedback
                let generator = UINotificationFeedbackGenerator()
                generator.notificationOccurred(.success)
                
                // Hide animation after delay
                DispatchQueue.main.asyncAfter(deadline: .now() + 2) {
                    withAnimation {
                        showAddedAnimation = false
                    }
                }
            } catch {
                print("Failed to save habit: \(error)")
            }
        }
    }
    
    private func deleteHabit(_ habit: Habit) {
        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
            modelContext.delete(habit)
            
            do {
                try modelContext.save()
                
                // Haptic feedback
                let generator = UINotificationFeedbackGenerator()
                generator.notificationOccurred(.success)
            } catch {
                print("Failed to delete habit: \(error)")
            }
        }
    }
}



#Preview {
    ActivityView(showHabitManagement: .constant(false))
        .modelContainer(for: [Habit.self, HabitLog.self], inMemory: true)
}
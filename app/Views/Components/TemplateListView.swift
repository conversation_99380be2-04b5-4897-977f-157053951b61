//
//  TemplateListView.swift
//  LightningHabit
//
//  Unified template browsing experience for both ActivityView and HabitManagementView
//

import SwiftUI
import SwiftData

struct TemplateListView: View {
    @Environment(\.modelContext) private var modelContext
    @Query(sort: \Habit.order) private var existingHabits: [Habit]
    
    @Binding var selectedCategory: TemplateCategory
    let onAddTemplate: (HabitTemplate) -> Void
    let showCloseButton: Bool
    let onClose: (() -> Void)?
    
    init(
        selectedCategory: Binding<TemplateCategory>,
        onAddTemplate: @escaping (HabitTemplate) -> Void,
        showCloseButton: Bool = false,
        onClose: (() -> Void)? = nil
    ) {
        self._selectedCategory = selectedCategory
        self.onAddTemplate = onAddTemplate
        self.showCloseButton = showCloseButton
        self.onClose = onClose
    }
    
    private var templatesForCategory: [HabitTemplate] {
        HabitTemplate.templates(for: selectedCategory)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text(String(localized: "choose_template"))
                        .font(.system(size: 24, weight: .bold))
                        .foregroundColor(.habitTextPrimary)
                        .padding(.top, 8)
                    
                    Text(String(localized: "popular_habit_templates"))
                        .font(.system(size: 14))
                        .foregroundColor(.habitTextSecondary)
                }
                
                Spacer()
                
                if showCloseButton {
                    Button(action: {
                        onClose?()
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 24))
                            .foregroundColor(.habitTextSecondary.opacity(0.5))
                    }
                }
            }
            
            // Category tabs
            CategoryTabBar(selectedCategory: $selectedCategory)
            
            // Template grid
            ScrollView(.vertical, showsIndicators: false) {
                LazyVGrid(columns: [
                    GridItem(.flexible(), spacing: 16),
                    GridItem(.flexible(), spacing: 16)
                ], spacing: 16) {
                    ForEach(templatesForCategory) { template in
                        let isAlreadyAdded = existingHabits.contains { habit in
                            habit.name.lowercased() == template.name.lowercased()
                        }
                        
                        TemplateCard(
                            template: template,
                            onAdd: {
                                if !isAlreadyAdded {
                                    onAddTemplate(template)
                                }
                            },
                            isAlreadyAdded: isAlreadyAdded
                        )
                        .opacity(isAlreadyAdded ? 0.5 : 1.0)
                        .animation(.easeInOut(duration: 0.2), value: isAlreadyAdded)
                    }
                }
                .padding(.horizontal, 4)
            }
            .padding(.horizontal, -4)
        }
    }
}

// MARK: - Supporting Views

struct TemplateCard: View {
    let template: HabitTemplate
    let onAdd: () -> Void
    let isAlreadyAdded: Bool
    
    @State private var isPressed = false
    
    var body: some View {
        VStack(alignment: .leading, spacing: 0) {
            // Header with icon and add button
            HStack {
                // Template icon
                Image(systemName: template.icon)
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(.habitPurple.opacity(0.8))
                    .frame(width: 40, height: 40)
                    .background(
                        Circle()
                            .fill(Color.habitPurple.opacity(0.1))
                    )
                
                Spacer()
                
                // Add button or checkmark
                if isAlreadyAdded {
                    Image(systemName: "checkmark.circle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(.habitSuccess)
                } else {
                    Button(action: {
                        withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                            onAdd()
                        }
                    }) {
                        Image(systemName: "plus.circle.fill")
                            .font(.system(size: 20))
                            .foregroundColor(.habitPurple)
                    }
                }
            }
            
            // Template info
            VStack(alignment: .leading, spacing: 4) {
                Text(template.name)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.habitTextPrimary)
                    .lineLimit(1)
                
                Text(template.description)
                    .font(.system(size: 13))
                    .foregroundColor(.habitTextSecondary)
                    .lineLimit(2)
                    .fixedSize(horizontal: false, vertical: true)
            }
            .padding(.top, 12)
            
            // Template badge
            HStack {
                if template.defaultReminderEnabled {
                    HStack(spacing: 4) {
                        Image(systemName: "bell.fill")
                            .font(.system(size: 10))
                        Text(String(localized: "reminder"))
                            .font(.system(size: 11, weight: .medium))
                    }
                    .foregroundColor(.habitPurple.opacity(0.8))
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(
                        Capsule()
                            .fill(Color.habitPurple.opacity(0.1))
                    )
                }
                
                Spacer()
            }
            .padding(.top, 12)
        }
        .padding(16)
        .frame(minHeight: 160)
        .frame(maxWidth: .infinity)
        .background(
            RoundedRectangle(cornerRadius: 20)
                .fill(Color.habitCardBackground)
                .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 4)
        )
        .overlay(
            RoundedRectangle(cornerRadius: 20)
                .stroke(isAlreadyAdded ? Color.habitSuccess.opacity(0.3) : Color.clear, lineWidth: 2)
        )
        .scaleEffect(isPressed ? 0.95 : 1.0)
        .onTapGesture {
            if !isAlreadyAdded {
                withAnimation(.spring(response: 0.2, dampingFraction: 0.8)) {
                    isPressed = true
                }
                
                DispatchQueue.main.asyncAfter(deadline: .now() + 0.1) {
                    withAnimation(.spring(response: 0.2, dampingFraction: 0.8)) {
                        isPressed = false
                    }
                    onAdd()
                }
            }
        }
    }
}

struct CategoryTabBar: View {
    @Binding var selectedCategory: TemplateCategory
    
    var body: some View {
        ScrollView(.horizontal, showsIndicators: false) {
            HStack(spacing: 12) {
                ForEach(TemplateCategory.allCases, id: \.self) { category in
                    CategoryTab(
                        category: category,
                        isSelected: selectedCategory == category,
                        action: {
                            withAnimation(.spring(response: 0.3, dampingFraction: 0.8)) {
                                selectedCategory = category
                            }
                        }
                    )
                }
            }
            .padding(.horizontal, 4)
        }
        .padding(.horizontal, -4)
    }
}

struct CategoryTab: View {
    let category: TemplateCategory
    let isSelected: Bool
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack(spacing: 8) {
                Image(systemName: category.icon)
                    .font(.system(size: 14, weight: .medium))
                
                Text(LocalizedStringKey(category.rawValue))
                    .font(.system(size: 14, weight: .medium))
            }
            .foregroundColor(isSelected ? .white : .habitTextPrimary)
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .background(
                Capsule()
                    .fill(isSelected ? AnyShapeStyle(LinearGradient.habitPurpleGradient) : AnyShapeStyle(Color.habitCardBackground))
            )
            .overlay(
                Capsule()
                    .stroke(isSelected ? Color.clear : Color.habitTextSecondary.opacity(0.2), lineWidth: 1)
            )
        }
    }
}
//
//  TemplateGridView.swift
//  LightningHabit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 23..
//

import SwiftUI

struct TemplateGridView: View {
    let templates: [HabitTemplate]
    let onAddTemplate: (HabitTemplate) -> Void
    
    private let columns = [
        GridItem(.flexible(), spacing: 16),
        GridItem(.flexible(), spacing: 16)
    ]
    
    var body: some View {
        LazyVGrid(columns: columns, spacing: 16) {
            ForEach(Array(templates.enumerated()), id: \.element.id) { index, template in
                EnhancedTemplateCard(template: template) {
                    onAddTemplate(template)
                }
                .transition(.asymmetric(
                    insertion: .scale.combined(with: .opacity)
                        .animation(.spring(response: 0.4, dampingFraction: 0.8)
                        .delay(Double(index) * 0.05)),
                    removal: .scale.combined(with: .opacity)
                        .animation(.spring(response: 0.3, dampingFraction: 0.8))
                ))
            }
        }
        .animation(.spring(response: 0.4, dampingFraction: 0.8), value: templates.count)
    }
}

struct TemplateGridEmptyState: View {
    let searchQuery: String
    
    var body: some View {
        VStack(spacing: 16) {
            Image(systemName: "magnifyingglass")
                .font(.system(size: 48))
                .foregroundColor(.habitTextSecondary.opacity(0.5))
            
            VStack(spacing: 8) {
                Text(String(localized: "no_templates_found"))
                    .font(.system(size: 20, weight: .semibold))
                    .foregroundColor(.habitTextPrimary)
                
                if !searchQuery.isEmpty {
                    Text(String(localized: "try_different_keywords", defaultValue: "Try searching for '%@' with different keywords", table: nil).replacingOccurrences(of: "%@", with: searchQuery))
                        .font(.system(size: 14))
                        .foregroundColor(.habitTextSecondary)
                        .multilineTextAlignment(.center)
                } else {
                    Text(String(localized: "try_different_category"))
                        .font(.system(size: 14))
                        .foregroundColor(.habitTextSecondary)
                }
            }
        }
        .padding(.vertical, 40)
    }
}

#Preview {
    VStack(spacing: 32) {
        ScrollView {
            TemplateGridView(templates: Array(HabitTemplate.allTemplates.prefix(6))) { _ in }
                .padding()
        }
        
        TemplateGridEmptyState(searchQuery: "nonexistent")
            .padding()
    }
    .background(Color.habitBackground)
}
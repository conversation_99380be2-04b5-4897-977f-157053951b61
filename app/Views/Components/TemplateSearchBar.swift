//
//  TemplateSearchBar.swift
//  LightningHabit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 23..
//

import SwiftUI

struct TemplateSearchBar: View {
    @Binding var searchText: String
    @FocusState private var isSearchFocused: Bool
    
    var body: some View {
        HStack(spacing: 12) {
            // Search Icon
            Image(systemName: "magnifyingglass")
                .font(.system(size: 16, weight: .medium))
                .foregroundColor(.habitTextSecondary)
            
            // Search TextField
            TextField("Search templates...", text: $searchText)
                .font(.system(size: 16))
                .foregroundColor(.habitTextPrimary)
                .focused($isSearchFocused)
                .submitLabel(.search)
            
            // Clear Button
            if !searchText.isEmpty {
                Button(action: {
                    searchText = ""
                    isSearchFocused = false
                }) {
                    Image(systemName: "xmark.circle.fill")
                        .font(.system(size: 16))
                        .foregroundColor(.habitTextSecondary)
                }
                .transition(.scale.combined(with: .opacity))
            }
        }
        .padding(.horizontal, 16)
        .padding(.vertical, 12)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.habitSurfaceLight)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(isSearchFocused ? Color.habitPurple.opacity(0.5) : Color.habitDivider.opacity(0.3), lineWidth: 1)
                )
        )
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isSearchFocused)
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: searchText.isEmpty)
    }
}

#Preview {
    VStack(spacing: 20) {
        TemplateSearchBar(searchText: .constant(""))
        TemplateSearchBar(searchText: .constant("Morning"))
    }
    .padding()
    .background(Color.habitBackground)
}
//
//  EnhancedTemplateCard.swift
//  LightningHabit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 23..
//

import SwiftUI

struct EnhancedTemplateCard: View {
    let template: HabitTemplate
    let onAdd: () -> Void
    
    @State private var isPressed = false
    @State private var showingDetails = false
    
    private var popularityStars: Int {
        // Convert popularity (0-10) to stars (1-5)
        return min(5, max(1, (template.popularity + 1) / 2))
    }
    
    private var reminderTimeString: String? {
        guard template.defaultReminderEnabled,
              let time = template.defaultReminderTime else { return nil }
        
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: time)
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            // Header with icon and add button
            HStack {
                // Category-colored icon background
                ZStack {
                    Circle()
                        .fill(
                            LinearGradient(
                                colors: [
                                    template.category.color.opacity(0.8),
                                    template.category.color
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            )
                        )
                        .frame(width: 48, height: 48)
                        .shadow(color: template.category.color.opacity(0.3), radius: 4, x: 0, y: 2)
                    
                    Image(systemName: template.icon)
                        .font(.system(size: 22, weight: .medium))
                        .foregroundColor(.white)
                }
                
                Spacer()
                
                // Add button with pulse animation
                Button(action: {
                    impactFeedback(.medium)
                    withAnimation(.spring(response: 0.4, dampingFraction: 0.6)) {
                        onAdd()
                    }
                }) {
                    ZStack {
                        Circle()
                            .fill(Color.white)
                            .frame(width: 32, height: 32)
                            .shadow(color: .habitShadow, radius: 2, x: 0, y: 1)
                        
                        Image(systemName: "plus")
                            .font(.system(size: 16, weight: .bold))
                            .foregroundColor(.habitPurple)
                    }
                }
                .scaleEffect(isPressed ? 0.9 : 1.0)
                .animation(.spring(response: 0.2, dampingFraction: 0.6), value: isPressed)
                .onLongPressGesture(minimumDuration: 0, maximumDistance: .infinity, pressing: { pressing in
                    isPressed = pressing
                }, perform: {})
            }
            
            // Template info
            VStack(alignment: .leading, spacing: 6) {
                Text(template.name)
                    .font(.system(size: 16, weight: .semibold))
                    .foregroundColor(.habitTextPrimary)
                    .lineLimit(1)
                
                Text(template.description)
                    .font(.system(size: 13))
                    .foregroundColor(.habitTextSecondary)
                    .lineLimit(2)
                    .multilineTextAlignment(.leading)
            }
            
            // Footer with metadata
            HStack(spacing: 8) {
                // Popularity stars
                HStack(spacing: 2) {
                    ForEach(0..<popularityStars, id: \.self) { _ in
                        Image(systemName: "star.fill")
                            .font(.system(size: 8))
                            .foregroundColor(.habitOrange)
                    }
                    
                    ForEach(popularityStars..<5, id: \.self) { _ in
                        Image(systemName: "star")
                            .font(.system(size: 8))
                            .foregroundColor(.habitOrange.opacity(0.3))
                    }
                }
                
                Spacer()
                
                // Reminder indicator
                if let reminderTime = reminderTimeString {
                    HStack(spacing: 4) {
                        Image(systemName: "bell.fill")
                            .font(.system(size: 10))
                            .foregroundColor(.habitOrange)
                        
                        Text(reminderTime)
                            .font(.system(size: 10, weight: .medium))
                            .foregroundColor(.habitTextSecondary)
                    }
                }
                
                // Category badge
                Text(template.category.rawValue.uppercased())
                    .font(.system(size: 9, weight: .bold))
                    .foregroundColor(template.category.color.opacity(0.8))
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(
                        Capsule()
                            .fill(template.category.color.opacity(0.1))
                    )
            }
        }
        .padding(16)
        .background(
            RoundedRectangle(cornerRadius: 16)
                .fill(Color.white)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(
                            LinearGradient(
                                colors: [
                                    template.category.color.opacity(0.2),
                                    template.category.color.opacity(0.1)
                                ],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            lineWidth: 1
                        )
                )
                .shadow(color: .habitShadow, radius: 6, x: 0, y: 3)
        )
        .scaleEffect(isPressed ? 0.98 : 1.0)
        .animation(.spring(response: 0.3, dampingFraction: 0.8), value: isPressed)
    }
    
    private func impactFeedback(_ style: UIImpactFeedbackGenerator.FeedbackStyle) {
        let impact = UIImpactFeedbackGenerator(style: style)
        impact.impactOccurred()
    }
}

#Preview {
    let sampleTemplate = HabitTemplate(
        name: "Morning Run",
        icon: "figure.run",
        description: "Start your day with energizing cardio",
        category: .fitness,
        defaultReminderEnabled: true,
        defaultReminderTime: Calendar.current.date(from: DateComponents(hour: 7, minute: 0)),
        popularity: 9
    )
    
    VStack(spacing: 16) {
        LazyVGrid(columns: [
            GridItem(.flexible()),
            GridItem(.flexible())
        ], spacing: 16) {
            EnhancedTemplateCard(template: sampleTemplate) { }
            EnhancedTemplateCard(template: HabitTemplate(
                name: "Drink Water",
                icon: "drop.fill",
                description: "Stay hydrated throughout the day",
                category: .health,
                popularity: 10
            )) { }
        }
    }
    .padding()
    .background(Color.habitBackground)
}
//
//  CalendarMonthView.swift
//  LightningHabit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 23..
//

import SwiftUI
import SwiftData

struct CalendarMonthView: View {
    @Query(sort: \Habit.order) private var habits: [Habit]
    
    private let calendar = Calendar.current
    private let today = Date()
    
    private var monthDays: [Date] {
        guard let monthInterval = calendar.dateInterval(of: .month, for: today),
              let monthFirstWeek = calendar.dateInterval(of: .weekOfMonth, for: monthInterval.start),
              let monthLastWeek = calendar.dateInterval(of: .weekOfMonth, for: monthInterval.end) else {
            return []
        }
        
        let numberOfDays = calendar.dateComponents([.day], from: monthFirstWeek.start, to: monthLastWeek.end).day ?? 0
        
        return (0..<numberOfDays).compactMap { day in
            calendar.date(byAdding: .day, value: day, to: monthFirstWeek.start)
        }
    }
    
    private var weekdayHeaders: [String] {
        let formatter = DateFormatter()
        formatter.calendar = calendar
        return formatter.shortWeekdaySymbols.map { String($0.prefix(3)) }
    }
    
    var body: some View {
        VStack(spacing: 16) {
            // Month and year header
            HStack {
                Text(monthYearString)
                    .font(.system(size: 18, weight: .semibold))
                    .foregroundColor(.habitTextPrimary)
                Spacer()
            }
            
            // Weekday headers
            HStack(spacing: 0) {
                ForEach(weekdayHeaders, id: \.self) { weekday in
                    Text(weekday)
                        .font(.system(size: 12, weight: .medium))
                        .foregroundColor(.habitTextSecondary)
                        .frame(maxWidth: .infinity)
                }
            }
            
            // Calendar grid
            LazyVGrid(columns: Array(repeating: GridItem(.flexible(), spacing: 8), count: 7), spacing: 8) {
                ForEach(monthDays, id: \.self) { date in
                    DayCell(date: date, habits: habits)
                }
            }
            
            // Legend
            HStack(spacing: 20) {
                HStack(spacing: 8) {
                    Circle()
                        .fill(Color.habitOrange)
                        .frame(width: 8, height: 8)
                    Text(String(localized: "less_than_70"))
                        .font(.system(size: 12))
                        .foregroundColor(.habitTextSecondary)
                }
                
                HStack(spacing: 8) {
                    Circle()
                        .fill(Color.habitPurple)
                        .frame(width: 8, height: 8)
                    Text(String(localized: "between_70_99"))
                        .font(.system(size: 12))
                        .foregroundColor(.habitTextSecondary)
                }
                
                HStack(spacing: 8) {
                    Circle()
                        .fill(Color.habitSuccess)
                        .frame(width: 8, height: 8)
                    Text(String(localized: "hundred_percent"))
                        .font(.system(size: 12))
                        .foregroundColor(.habitTextSecondary)
                }
            }
        }
    }
    
    private var monthYearString: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MMMM yyyy"
        return formatter.string(from: today)
    }
}

struct DayCell: View {
    let date: Date
    let habits: [Habit]
    
    private var calendar: Calendar { Calendar.current }
    
    private var isCurrentMonth: Bool {
        calendar.isDate(date, equalTo: Date(), toGranularity: .month)
    }
    
    private var isToday: Bool {
        calendar.isDateInToday(date)
    }
    
    private var isFuture: Bool {
        date > Date()
    }
    
    private var dayNumber: String {
        "\(calendar.component(.day, from: date))"
    }
    
    private var completionData: (completed: Int, total: Int) {
        let dayStart = calendar.startOfDay(for: date)
        let dayEnd = calendar.date(byAdding: .day, value: 1, to: dayStart)!
        
        var completed = 0
        var total = 0
        
        for habit in habits {
            let logs = habit.logs
            if !logs.isEmpty {
                total += 1
                for log in logs {
                    if log.date >= dayStart && log.date < dayEnd && log.status == .completed {
                        completed += 1
                        break
                    }
                }
            }
        }
        
        return (completed, total)
    }
    
    private var completionPercentage: Double {
        let data = completionData
        guard data.total > 0 else { return 0 }
        return Double(data.completed) / Double(data.total)
    }
    
    private var cellColor: Color {
        if !isCurrentMonth || isFuture {
            return Color.habitPurple.opacity(0.05)
        }
        
        let percentage = completionPercentage
        if percentage == 0 {
            return Color.habitOrange.opacity(0.2)
        } else if percentage < 0.7 {
            return Color.habitOrange.opacity(0.5)
        } else if percentage < 1.0 {
            return Color.habitPurple.opacity(0.7)
        } else {
            return Color.habitSuccess.opacity(0.7)
        }
    }
    
    var body: some View {
        VStack(spacing: 4) {
            Text(dayNumber)
                .font(.system(size: 14, weight: isToday ? .bold : .medium))
                .foregroundColor(isCurrentMonth && !isFuture ? .habitTextPrimary : .habitTextTertiary)
                .frame(width: 36, height: 36)
                .background(
                    RoundedRectangle(cornerRadius: 8)
                        .fill(cellColor)
                        .overlay(
                            RoundedRectangle(cornerRadius: 8)
                                .stroke(isToday ? Color.habitPurple : Color.clear, lineWidth: 2)
                        )
                )
            
            if isCurrentMonth && !isFuture && completionData.total > 0 {
                Text("\(completionData.completed)/\(completionData.total)")
                    .font(.system(size: 9, weight: .medium))
                    .foregroundColor(.habitTextSecondary)
            }
        }
    }
}

#Preview {
    CalendarMonthView()
        .padding()
}
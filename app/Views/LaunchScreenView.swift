//
//  LaunchScreenView.swift
//  LightningHabit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 24..
//

import SwiftUI

struct LaunchScreenView: View {
    var body: some View {
        ZStack {
            // Use habit background color
            Color.habitBackground
                .ignoresSafeArea()
            
            VStack(spacing: 24) {
                // SwiftHabits logo
                Image("swifthabits")
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(maxWidth: 280, maxHeight: 280)
                    .shadow(color: Color.black.opacity(0.1), radius: 10)

                // Loading indicator
                ProgressView()
                    .progressViewStyle(CircularProgressViewStyle(tint: .habitPurple))
                    .scaleEffect(0.8)
            }
        }
    }
}

#Preview {
    LaunchScreenView()
}
//
//  EmptyStateCard.swift
//  LightningHabit
//
//  Empty state card with onboarding hints
//

import SwiftUI

struct EmptyStateCard: View {
    @State private var showSwipeHint = true
    @State private var dragOffset: CGSize = .zero
    @State private var animatingHint = false
    
    var onAddHabit: () -> Void
    
    var body: some View {
        ZStack {
            // Main card
            VStack(spacing: 24) {
                // Empty state icon
                ZStack {
                    Circle()
                        .fill(LinearGradient.habitPurpleGradient.opacity(0.1))
                        .frame(width: 120, height: 120)
                    
                    Image(systemName: "plus.circle.fill")
                        .font(.system(size: 60))
                        .foregroundStyle(LinearGradient.habitPurpleGradient)
                }
                
                VStack(spacing: 12) {
                    Text(String(localized: "welcome_to_app"))
                        .font(.system(size: 28, weight: .bold))
                        .foregroundColor(.habitTextPrimary)
                    
                    Text(String(localized: "start_your_journey"))
                        .font(.system(size: 16))
                        .foregroundColor(.habitTextSecondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 20)
                }
                
                // Add habit button
                Button(action: onAddHabit) {
                    HStack(spacing: 12) {
                        Image(systemName: "plus.circle")
                            .font(.system(size: 20))
                        
                        Text(String(localized: "add_your_first_habit"))
                            .font(.system(size: 18, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .padding(.horizontal, 32)
                    .padding(.vertical, 16)
                    .background(LinearGradient.habitPurpleGradient)
                    .cornerRadius(30)
                }
                
                // Swipe hint
                if showSwipeHint {
                    VStack(spacing: 8) {
                        HStack(spacing: 20) {
                            HStack(spacing: 8) {
                                Image(systemName: "arrow.right.circle.fill")
                                    .font(.system(size: 20))
                                    .foregroundColor(.habitSuccess)
                                Text(String(localized: "swipe_right_done"))
                                    .font(.system(size: 14))
                                    .foregroundColor(.habitTextSecondary)
                            }
                            
                            HStack(spacing: 8) {
                                Image(systemName: "arrow.left.circle.fill")
                                    .font(.system(size: 20))
                                    .foregroundColor(.habitOrange)
                                Text(String(localized: "swipe_left_skip"))
                                    .font(.system(size: 14))
                                    .foregroundColor(.habitTextSecondary)
                            }
                        }
                        
                        Text(String(localized: "or_use_buttons"))
                            .font(.system(size: 12))
                            .foregroundColor(.habitTextTertiary)
                    }
                    .padding(.top, 12)
                    .opacity(animatingHint ? 0.4 : 1.0)
                    .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: animatingHint)
                }
            }
            .padding(.vertical, 40)
            .padding(.horizontal, 20)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 30)
                    .fill(.ultraThinMaterial)
                    .background(
                        RoundedRectangle(cornerRadius: 30)
                            .fill(Color.white)
                    )
                    .shadow(color: .habitShadow, radius: 20, x: 0, y: 10)
            )
            .offset(dragOffset)
            .rotationEffect(.degrees(Double(dragOffset.width / 20)))
            .gesture(
                DragGesture()
                    .onChanged { value in
                        withAnimation(.interactiveSpring()) {
                            dragOffset = value.translation
                        }
                    }
                    .onEnded { value in
                        withAnimation(.spring(response: 0.5, dampingFraction: 0.6)) {
                            dragOffset = .zero
                        }
                        
                        // Hide hint after first interaction
                        if abs(value.translation.width) > 50 {
                            withAnimation {
                                showSwipeHint = false
                            }
                        }
                    }
            )
            
            // Animated arrows overlay
            if showSwipeHint {
                HStack {
                    // Left arrow
                    Image(systemName: "chevron.left")
                        .font(.system(size: 30, weight: .bold))
                        .foregroundColor(.habitOrange.opacity(0.3))
                        .offset(x: animatingHint ? -10 : 0)
                    
                    Spacer()
                    
                    // Right arrow
                    Image(systemName: "chevron.right")
                        .font(.system(size: 30, weight: .bold))
                        .foregroundColor(.habitSuccess.opacity(0.3))
                        .offset(x: animatingHint ? 10 : 0)
                }
                .padding(.horizontal, 40)
                .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: animatingHint)
            }
        }
        .onAppear {
            animatingHint = true
        }
    }
}

#Preview {
    EmptyStateCard(onAddHabit: {})
        .padding()
        .background(Color.habitBackground)
}
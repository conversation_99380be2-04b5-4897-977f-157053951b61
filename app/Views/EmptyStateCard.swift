//
//  EmptyStateCard.swift
//  LightningHabit
//
//  Empty state card with onboarding hints
//

import SwiftUI

struct EmptyStateCard: View {
    @State private var showSwipeHint = true
    @State private var dragOffset: CGSize = .zero
    @State private var animatingHint = false
    @State private var sparkleAnimation = false
    @State private var selectedQuickHabit: Int? = nil
    @State private var showingAddHabit = false
    
    var onAddHabit: () -> Void
    
    private let quickHabits = [
        (icon: "drop.fill", name: "drink_water", color: Color.blue),
        (icon: "figure.walk", name: "daily_walk", color: Color.habitSuccess),
        (icon: "book.fill", name: "read_daily", color: Color.habitOrange),
        (icon: "moon.fill", name: "sleep_early", color: Color.habitPurple)
    ]
    
    var body: some View {
        ZStack {
            // Main card
            VStack(spacing: 32) {
                // Animated illustration
                ZStack {
                    // Background circles
                    ForEach(0..<3) { index in
                        Circle()
                            .fill(LinearGradient.habitPurpleGradient.opacity(0.1))
                            .frame(width: 140 - CGFloat(index * 20), height: 140 - CGFloat(index * 20))
                            .scaleEffect(sparkleAnimation ? 1.1 : 0.9)
                            .animation(
                                .easeInOut(duration: 2.0)
                                .repeatForever(autoreverses: true)
                                .delay(Double(index) * 0.2),
                                value: sparkleAnimation
                            )
                    }
                    
                    // Sparkle effects
                    ForEach(0..<4) { index in
                        Image(systemName: "sparkle")
                            .font(.system(size: 16))
                            .foregroundColor(.habitPurple)
                            .offset(
                                x: cos(Double(index) * .pi / 2) * 60,
                                y: sin(Double(index) * .pi / 2) * 60
                            )
                            .scaleEffect(sparkleAnimation ? 1.0 : 0.5)
                            .opacity(sparkleAnimation ? 0.8 : 0.3)
                            .animation(
                                .easeInOut(duration: 1.5)
                                .repeatForever(autoreverses: true)
                                .delay(Double(index) * 0.3),
                                value: sparkleAnimation
                            )
                    }
                    
                    // Center icon
                    Image(systemName: "star.fill")
                        .font(.system(size: 50))
                        .foregroundStyle(LinearGradient.habitPurpleGradient)
                        .rotationEffect(.degrees(sparkleAnimation ? 10 : -10))
                        .animation(
                            .easeInOut(duration: 3.0)
                            .repeatForever(autoreverses: true),
                            value: sparkleAnimation
                        )
                }
                .frame(height: 140)
                
                VStack(spacing: 16) {
                    Text(String(localized: "ready_to_start"))
                        .font(.system(size: 32, weight: .bold))
                        .foregroundColor(.habitTextPrimary)
                    
                    Text(String(localized: "build_better_habits"))
                        .font(.system(size: 18))
                        .foregroundColor(.habitTextSecondary)
                        .multilineTextAlignment(.center)
                        .padding(.horizontal, 40)
                }
                
                // Quick start habits
                VStack(spacing: 16) {
                    Text(String(localized: "quick_start"))
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.habitTextTertiary)
                    
                    LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 12) {
                        ForEach(0..<quickHabits.count, id: \.self) { index in
                            QuickHabitButton(
                                icon: quickHabits[index].icon,
                                name: quickHabits[index].name,
                                color: quickHabits[index].color,
                                isSelected: selectedQuickHabit == index,
                                action: {
                                    withAnimation(.spring(response: 0.3, dampingFraction: 0.7)) {
                                        if selectedQuickHabit == index {
                                            selectedQuickHabit = nil
                                        } else {
                                            selectedQuickHabit = index
                                        }
                                    }
                                    
                                    // Auto add habit after selection
                                    if selectedQuickHabit != nil {
                                        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                                            onAddHabit()
                                        }
                                    }
                                }
                            )
                        }
                    }
                }
                .padding(.horizontal, 20)
                
                // Custom habit button
                Button(action: onAddHabit) {
                    HStack(spacing: 12) {
                        Image(systemName: "plus")
                            .font(.system(size: 18, weight: .semibold))
                        
                        Text(String(localized: "create_custom_habit"))
                            .font(.system(size: 17, weight: .semibold))
                    }
                    .foregroundColor(.white)
                    .frame(maxWidth: .infinity)
                    .padding(.vertical, 18)
                    .background(LinearGradient.habitPurpleGradient)
                    .cornerRadius(16)
                    .shadow(color: .habitPurple.opacity(0.3), radius: 10, x: 0, y: 5)
                }
                .padding(.horizontal, 20)
                
                // Swipe hint
                if showSwipeHint {
                    VStack(spacing: 8) {
                        HStack(spacing: 20) {
                            HStack(spacing: 8) {
                                Image(systemName: "arrow.right.circle.fill")
                                    .font(.system(size: 20))
                                    .foregroundColor(.habitSuccess)
                                Text(String(localized: "swipe_right_done"))
                                    .font(.system(size: 14))
                                    .foregroundColor(.habitTextSecondary)
                            }
                            
                            HStack(spacing: 8) {
                                Image(systemName: "arrow.left.circle.fill")
                                    .font(.system(size: 20))
                                    .foregroundColor(.habitOrange)
                                Text(String(localized: "swipe_left_skip"))
                                    .font(.system(size: 14))
                                    .foregroundColor(.habitTextSecondary)
                            }
                        }
                        
                        Text(String(localized: "or_use_buttons"))
                            .font(.system(size: 12))
                            .foregroundColor(.habitTextTertiary)
                    }
                    .padding(.top, 12)
                    .opacity(animatingHint ? 0.4 : 1.0)
                    .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: animatingHint)
                }
            }
            .padding(.vertical, 40)
            .padding(.horizontal, 20)
            .frame(maxWidth: .infinity)
            .background(
                RoundedRectangle(cornerRadius: 30)
                    .fill(.ultraThinMaterial)
                    .background(
                        RoundedRectangle(cornerRadius: 30)
                            .fill(Color.white)
                    )
                    .shadow(color: .habitShadow, radius: 20, x: 0, y: 10)
            )
            .offset(dragOffset)
            .rotationEffect(.degrees(Double(dragOffset.width / 20)))
            .gesture(
                DragGesture()
                    .onChanged { value in
                        withAnimation(.interactiveSpring()) {
                            dragOffset = value.translation
                        }
                    }
                    .onEnded { value in
                        withAnimation(.spring(response: 0.5, dampingFraction: 0.6)) {
                            dragOffset = .zero
                        }
                        
                        // Hide hint after first interaction
                        if abs(value.translation.width) > 50 {
                            withAnimation {
                                showSwipeHint = false
                            }
                        }
                    }
            )
            
            // Animated arrows overlay
            if showSwipeHint {
                HStack {
                    // Left arrow
                    Image(systemName: "chevron.left")
                        .font(.system(size: 30, weight: .bold))
                        .foregroundColor(.habitOrange.opacity(0.3))
                        .offset(x: animatingHint ? -10 : 0)
                    
                    Spacer()
                    
                    // Right arrow
                    Image(systemName: "chevron.right")
                        .font(.system(size: 30, weight: .bold))
                        .foregroundColor(.habitSuccess.opacity(0.3))
                        .offset(x: animatingHint ? 10 : 0)
                }
                .padding(.horizontal, 40)
                .animation(.easeInOut(duration: 1.5).repeatForever(autoreverses: true), value: animatingHint)
            }
        }
        .onAppear {
            animatingHint = true
        }
    }
}

#Preview {
    EmptyStateCard(onAddHabit: {})
        .padding()
        .background(Color.habitBackground)
}
//
//  MainView.swift
//  LightningHabit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 23..
//

import SwiftUI
import SwiftData

struct MainView: View {
    @Environment(\.modelContext) private var modelContext
    @Query(
        filter: #Predicate<Habit> { $0.isActive },
        sort: \Habit.order
    ) private var habits: [Habit]
    
    @State private var currentHabitIndex = 0
    @State private var dragOffset: CGSize = .zero
    @State private var isDragging = false
    @State private var showAddHabit = false
    @Binding var selectedTab: String
    
    // Dopamine boost animation states
    @State private var showConfetti = false
    @State private var showMilestoneAnimation = false
    @State private var milestoneMessage = ""

    // Undo functionality
    @State private var showUndoButton = false
    @State private var lastAction: LastAction?
    @State private var undoTimer: Timer?

    struct LastAction {
        let habit: Habit
        let action: ActionType
        let timestamp: Date

        enum ActionType {
            case completed
            case skipped
        }
    }
    
    // Smart sorted habits by reminder time
    private var smartSortedHabits: [Habit] {
        let now = Date()
        let calendar = Calendar.current
        let currentTimeMinutes = calendar.component(.hour, from: now) * 60 + calendar.component(.minute, from: now)
        
        // Separate habits with and without reminders
        let habitsWithReminders = habits.filter { $0.reminderEnabled && $0.reminderTime != nil }
        let habitsWithoutReminders = habits.filter { !$0.reminderEnabled || $0.reminderTime == nil }
        
        // Sort habits with reminders by proximity to current time
        let sortedWithReminders = habitsWithReminders.sorted { habit1, habit2 in
            guard let time1 = habit1.reminderTime, let time2 = habit2.reminderTime else { return false }
            
            let minutes1 = calendar.component(.hour, from: time1) * 60 + calendar.component(.minute, from: time1)
            let minutes2 = calendar.component(.hour, from: time2) * 60 + calendar.component(.minute, from: time2)
            
            // Calculate time difference considering day wraparound
            let diff1 = (minutes1 - currentTimeMinutes + 1440) % 1440
            let diff2 = (minutes2 - currentTimeMinutes + 1440) % 1440
            
            return diff1 < diff2
        }
        
        // Sort habits without reminders by their order
        let sortedWithoutReminders = habitsWithoutReminders.sorted { $0.order < $1.order }
        
        // Combine: reminders first, then non-reminders
        return sortedWithReminders + sortedWithoutReminders
    }
    
    private var incompleteHabits: [Habit] {
        smartSortedHabits.filter { !$0.isCompletedToday }
    }
    
    private var currentHabit: Habit? {
        guard currentHabitIndex < incompleteHabits.count else { return nil }
        return incompleteHabits[currentHabitIndex]
    }
    
    private var todayProgress: (completed: Int, total: Int) {
        let completed = habits.filter { $0.isCompletedToday }.count
        return (completed, habits.count)
    }
    
    private var currentStreak: Int {
        habits.first?.currentStreak ?? 0
    }
    
    var body: some View {
        ZStack {
            // Liquid glass background
            backgroundGradient
            
            VStack(spacing: 0) {
                // Main habit card area
                habitCardArea
                
                // Progress bar
                if habits.count > 0 {
                    progressBar
                        .padding(.horizontal, 24)
                        .padding(.bottom, 16)
                }
                
                // Bottom stats area
                bottomStatsArea
            }
        }
        .preferredColorScheme(.light)
        .overlay {
            // Confetti animation
            if showConfetti {
                ConfettiView()
                    .allowsHitTesting(false)
                    .transition(.opacity)
            }
        }
        .overlay {
            // Milestone animation
            if showMilestoneAnimation {
                MilestoneAnimationView(message: milestoneMessage)
                    .allowsHitTesting(false)
                    .transition(.scale.combined(with: .opacity))
            }
        }
        .overlay(alignment: .bottom) {
            // Undo button
            if showUndoButton {
                undoButtonView
                    .transition(.move(edge: .bottom).combined(with: .opacity))
                    .animation(.standardUI, value: showUndoButton)
            }
        }
        .overlay(alignment: .topTrailing) {
            // Debug button (hidden, tap top-right corner 3 times)
            Button(action: {
                debugResetHabits()
            }) {
                Color.clear
                    .frame(width: 50, height: 50)
            }
        }
        .sheet(isPresented: $showAddHabit) {
            AddEditHabitView()
        }

    }
    
    // MARK: - Background
    private var backgroundGradient: some View {
        Color.habitBackground
            .ignoresSafeArea()
    }

    // MARK: - Undo Button
    private var undoButtonView: some View {
        HStack(spacing: 12) {
            Button(action: {
                undoLastAction()
            }) {
                HStack(spacing: 8) {
                    Image(systemName: "arrow.uturn.backward.circle.fill")
                        .font(.system(size: 20))
                        .foregroundColor(.white)

                    Text("undo".localized)
                        .font(.system(size: 16, weight: .medium))
                        .foregroundColor(.white)
                }
                .padding(.horizontal, 30)
                .padding(.vertical, 12)
                .background(
                    RoundedRectangle(cornerRadius: 25)
                        .fill(Color.black.opacity(0.8))
                        .overlay(
                            RoundedRectangle(cornerRadius: 25)
                                .stroke(Color.white.opacity(0.2), lineWidth: 1)
                        )
                )
            }
            .accessibilityLabel("undo_last_action".localized)
            .accessibilityHint("tap_to_undo".localized)
            .accessibilityAddTraits(.isButton)
            .scaleEffect(showUndoButton ? 1.0 : 0.8)
            .animation(.quickInteraction, value: showUndoButton)
        }
        .padding(.bottom, 200) // Above bottom navigation and progress bar
    }
    
    // MARK: - Habit Card Area
    private var habitCardArea: some View {
        GeometryReader { geometry in
            ZStack {
                if let habit = currentHabit {
                    HabitCard(
                        habit: habit,
                        dragOffset: $dragOffset,
                        isDragging: $isDragging,
                        onComplete: {
                            completeCurrentHabit()
                        },
                        onSkip: {
                            skipCurrentHabit()
                        }
                    )
                    .accessibilityElement(children: .contain)
                    .frame(width: geometry.size.width - 40)
                    .frame(maxHeight: 480)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2 - 40)
                } else if habits.isEmpty {
                    // No habits yet - show empty state with onboarding
                    EmptyStateCard(onAddHabit: {
                        showAddHabit = true
                    })
                    .frame(width: geometry.size.width - 40)
                    .frame(maxHeight: 480)
                    .position(x: geometry.size.width / 2, y: geometry.size.height / 2 - 40)
                } else {
                    // All habits completed
                    CompletionView()
                        .frame(width: geometry.size.width - 40)
                        .frame(maxHeight: 480)
                        .position(x: geometry.size.width / 2, y: geometry.size.height / 2 - 40)
                }
            }
        }
        .onAppear {
            // loadSampleDataIfNeeded() // Disabled to prevent auto-creation of habits
            requestNotificationPermission()
        }
    }
    
    // MARK: - Progress Bar
    private var progressBar: some View {
        VStack(spacing: 8) {
            // Progress text
            HStack {
                Text("x_of_y_completed".localized(with: todayProgress.completed, todayProgress.total))
                    .font(.system(size: 14, weight: .medium))
                    .foregroundColor(.habitTextSecondary)
                    .accessibilityLabel("progress_today".localized(with: todayProgress.completed, todayProgress.total))
                Spacer()
                // Don't show "Perfect day!" in header since CompletionView will show it
            }
            
            // Progress segments
            GeometryReader { geometry in
                HStack(spacing: 4) {
                    ForEach(0..<habits.count, id: \.self) { index in
                        let habit = smartSortedHabits[index]
                        let isCompleted = habit.isCompletedToday
                        
                        RoundedRectangle(cornerRadius: 4)
                            .fill(
                                isCompleted 
                                ? Color.habitSuccess
                                : Color.habitSuccess.opacity(0.2)
                            )
                            .frame(height: 8)
                            .animation(.standardUI, value: isCompleted)
                    }
                }
            }
            .frame(height: 8)
        }
    }
    
    // MARK: - Bottom Stats
    private var bottomStatsArea: some View {
        VStack(spacing: 0) {
            // Bottom navigation with purple gradient
            BottomNavigationBar(
                selectedTab: $selectedTab,
                onAddTap: {
                    showAddHabit = true
                }
            )
        }
    }
    
    // MARK: - Helpers
    private var currentDateFormatted: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "EEEE, MMMM d"
        return formatter.string(from: Date())
    }
    
    private var completionRate: Int {
        guard habits.count > 0 else { return 0 }
        return Int((Double(todayProgress.completed) / Double(todayProgress.total)) * 100)
    }
    
    private var weeklyCompletionRate: Int {
        // Calculate weekly completion rate from last 7 days
        let calendar = Calendar.current
        let today = Date()
        let weekAgo = calendar.date(byAdding: .day, value: -7, to: today)!
        
        var completedCount = 0
        var totalCount = 0
        
        for habit in habits {
            let logs = habit.logs
            if !logs.isEmpty {
                for log in logs {
                    if log.date >= weekAgo && log.date <= today {
                        totalCount += 1
                        if log.status == .completed {
                            completedCount += 1
                        }
                    }
                }
            }
        }
        
        guard totalCount > 0 else { return 0 }
        return Int((Double(completedCount) / Double(totalCount)) * 100)
    }
    
    private var weeklyCompletedCount: Int {
        let calendar = Calendar.current
        let today = Date()
        let weekAgo = calendar.date(byAdding: .day, value: -7, to: today)!
        
        var count = 0
        for habit in habits {
            let logs = habit.logs
            if !logs.isEmpty {
                for log in logs {
                    if log.date >= weekAgo && log.date <= today && log.status == .completed {
                        count += 1
                    }
                }
            }
        }
        return count
    }
    
    private var weeklyChartData: [WeeklyChart.DayData] {
        let calendar = Calendar.current
        let today = Date()
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "EEE"
        
        var data: [WeeklyChart.DayData] = []
        
        for dayOffset in (0..<7).reversed() {
            guard let date = calendar.date(byAdding: .day, value: -dayOffset, to: today) else { continue }
            
            let dayStart = calendar.startOfDay(for: date)
            let dayEnd = calendar.date(byAdding: .day, value: 1, to: dayStart)!
            
            var completed = 0
            let total = habits.count
            
            for habit in habits {
                let logs = habit.logs
            if !logs.isEmpty {
                    for log in logs {
                        if log.date >= dayStart && log.date < dayEnd && log.status == .completed {
                            completed += 1
                            break
                        }
                    }
                }
            }
            
            data.append(WeeklyChart.DayData(
                day: dateFormatter.string(from: date),
                completed: completed,
                total: total
            ))
        }
        
        return data
    }
    
    // MARK: - Actions
    private func completeCurrentHabit() {
        guard let habit = currentHabit else { return }

        // Store action for undo
        lastAction = LastAction(habit: habit, action: .completed, timestamp: Date())

        habit.markAsCompleted()
        try? modelContext.save()

        // Show undo button
        displayUndoButton()

        // Check for milestones and trigger animations
        checkAndTriggerMilestones(for: habit)

        // Move to next habit
        withAnimation(.standardUI) {
            if currentHabitIndex < incompleteHabits.count - 1 {
                currentHabitIndex += 1
            } else {
                currentHabitIndex = 0
            }
        }
    }
    
    private func skipCurrentHabit() {
        guard let habit = currentHabit else { return }

        // Store action for undo
        lastAction = LastAction(habit: habit, action: .skipped, timestamp: Date())

        habit.markAsSkipped()
        try? modelContext.save()

        // Show undo button
        displayUndoButton()

        // Move to next habit
        withAnimation(.standardUI) {
            if currentHabitIndex < incompleteHabits.count - 1 {
                currentHabitIndex += 1
            } else {
                currentHabitIndex = 0
            }
        }
    }
    
    private func requestNotificationPermission() {
        Task {
            await NotificationManager.shared.requestAuthorization()
        }
    }
    
    private func checkAndTriggerMilestones(for habit: Habit) {
        let completedCount = todayProgress.completed
        let totalCount = todayProgress.total
        
        // Haptic feedback for any completion
        let successHaptic = UINotificationFeedbackGenerator()
        successHaptic.notificationOccurred(.success)
        
        // Don't show any milestone if all habits are completed (CompletionView will be shown)
        if completedCount == totalCount && totalCount > 0 {
            // All habits completed - only show confetti, no milestone overlay
            // The CompletionView already shows the completion message
            showConfettiAnimation()
            return
        }
        
        // Check for streak milestones
        let streak = habit.currentStreak
        if [7, 30, 100, 365].contains(streak) {
            showMilestone(message: "\(streak) day streak! 🔥")
            return
        }
        
        // Check for daily completion milestones
        if completedCount % 3 == 0 && completedCount > 0 {
            // Every 3 habits
            showConfettiAnimation()
        }
    }
    
    private func showConfettiAnimation() {
        withAnimation(.celebration) {
            showConfetti = true
        }
        
        // Hide after animation
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            withAnimation(AnimationConstants.quickFadeOut) {
                showConfetti = false
            }
        }
    }
    
    private func showMilestone(message: String) {
        milestoneMessage = message
        withAnimation(.celebration) {
            showMilestoneAnimation = true
        }

        // Hide after animation
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.5) {
            withAnimation(AnimationConstants.quickFadeOut) {
                showMilestoneAnimation = false
            }
        }
    }

    // MARK: - Undo Functionality
    private func displayUndoButton() {
        // Cancel existing timer
        undoTimer?.invalidate()

        // Show undo button
        withAnimation(.standardUI) {
            showUndoButton = true
        }

        // Auto-hide after 4 seconds
        undoTimer = Timer.scheduledTimer(withTimeInterval: 4.0, repeats: false) { _ in
            withAnimation(.standardUI) {
                showUndoButton = false
                lastAction = nil
            }
        }
    }

    private func undoLastAction() {
        guard let action = lastAction else { return }

        // Reset the habit to pending state
        action.habit.resetToday()
        try? modelContext.save()

        // Hide undo button
        withAnimation(.standardUI) {
            showUndoButton = false
        }

        // Clear last action
        lastAction = nil
        undoTimer?.invalidate()

        // Haptic feedback
        let impact = UIImpactFeedbackGenerator(style: .light)
        impact.impactOccurred()

        // Move back to the undone habit if it's not the current one
        if let habitIndex = smartSortedHabits.firstIndex(where: { $0.id == action.habit.id }) {
            withAnimation(.standardUI) {
                currentHabitIndex = habitIndex
            }
        }
    }

    // MARK: - Debug Functions
    private func debugResetHabits() {
        // Reset all habits to pending for testing undo functionality
        for habit in smartSortedHabits {
            habit.resetToday()
        }
        try? modelContext.save()
    }

}

// MARK: - Stat Item Component
struct StatItem: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    let progress: Double? = nil
    
    var body: some View {
        VStack(spacing: 10) {
            ZStack {
                // Background circle for progress
                if let progress = progress {
                    Circle()
                        .stroke(Color.white.opacity(0.1), lineWidth: 4)
                        .frame(width: 56, height: 56)
                    
                    Circle()
                        .trim(from: 0, to: max(0, min(1, progress)))
                        .stroke(
                            LinearGradient(
                                colors: [color, color.opacity(0.6)],
                                startPoint: .topLeading,
                                endPoint: .bottomTrailing
                            ),
                            style: StrokeStyle(lineWidth: 4, lineCap: .round)
                        )
                        .rotationEffect(.degrees(-90))
                        .frame(width: 56, height: 56)
                }
                
                // Icon
                Image(systemName: icon)
                    .font(.system(size: 24, weight: .medium))
                    .foregroundColor(color)
                    .frame(width: 48, height: 48)
                    .background(
                        Circle()
                            .fill(color.opacity(0.15))
                    )
            }
            
            VStack(spacing: 4) {
                Text(value)
                    .font(.system(size: 22, weight: .bold))
                    .foregroundColor(.white)
                
                Text(title)
                    .font(.system(size: 13, weight: .medium))
                    .foregroundColor(.white.opacity(0.7))
            }
        }
    }
}

// MARK: - Confetti View
struct ConfettiView: View {
    @State private var particles: [ConfettiParticle] = []
    
    var body: some View {
        GeometryReader { geometry in
            ZStack {
                ForEach(particles) { particle in
                    Image(systemName: particle.symbol)
                        .font(.system(size: particle.size))
                        .foregroundColor(particle.color)
                        .position(particle.position)
                        .opacity(particle.opacity)
                        .rotationEffect(.degrees(particle.rotation))
                }
            }
            .onAppear {
                createParticles(in: geometry.size)
                animateParticles()
            }
        }
        .ignoresSafeArea()
    }
    
    private func createParticles(in size: CGSize) {
        let symbols = ["star.fill", "heart.fill", "sparkle", "circle.fill"]
        let colors: [Color] = [.habitPurple, .habitSuccess, .habitOrange, .habitPurple]
        
        particles = (0..<50).map { _ in
            ConfettiParticle(
                symbol: symbols.randomElement()!,
                color: colors.randomElement()!,
                size: CGFloat.random(in: 16...24),
                position: CGPoint(
                    x: CGFloat.random(in: 0...size.width),
                    y: -50
                ),
                velocity: CGPoint(
                    x: CGFloat.random(in: -100...100),
                    y: CGFloat.random(in: 300...600)
                ),
                rotation: Double.random(in: 0...360),
                rotationSpeed: Double.random(in: -180...180),
                opacity: 1.0
            )
        }
    }
    
    private func animateParticles() {
        Timer.scheduledTimer(withTimeInterval: 0.016, repeats: true) { timer in            
            for i in particles.indices {
                particles[i].position.y += particles[i].velocity.y * 0.016
                particles[i].position.x += particles[i].velocity.x * 0.016
                particles[i].velocity.y += 500 * 0.016 // gravity
                particles[i].rotation += particles[i].rotationSpeed * 0.016
                particles[i].opacity = max(0, particles[i].opacity - 0.016 * 0.5)
            }
            
            if particles.allSatisfy({ $0.opacity <= 0 }) {
                timer.invalidate()
            }
        }
    }
}

struct ConfettiParticle: Identifiable {
    let id = UUID()
    let symbol: String
    let color: Color
    let size: CGFloat
    var position: CGPoint
    var velocity: CGPoint
    var rotation: Double
    let rotationSpeed: Double
    var opacity: Double
}

// MARK: - Milestone Animation View
struct MilestoneAnimationView: View {
    let message: String
    @State private var scale: CGFloat = 0.5
    @State private var opacity: Double = 0
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "star.circle.fill")
                .font(.system(size: 80))
                .foregroundStyle(
                    LinearGradient(
                        colors: [Color.habitPurple, Color.habitSuccess],
                        startPoint: .topLeading,
                        endPoint: .bottomTrailing
                    )
                )
            
            Text(message)
                .font(.system(size: 28, weight: .bold, design: .rounded))
                .foregroundColor(.habitTextPrimary)
                .multilineTextAlignment(.center)
        }
        .padding(40)
        .background(
            RoundedRectangle(cornerRadius: 30)
                .fill(.ultraThinMaterial)
                .background(
                    RoundedRectangle(cornerRadius: 30)
                        .fill(Color.white.opacity(0.9))
                )
                .shadow(color: .habitShadow, radius: 20, x: 0, y: 10)
        )
        .scaleEffect(scale)
        .opacity(opacity)
        .onAppear {
            withAnimation(.celebration) {
                scale = 1.0
                opacity = 1.0
            }
        }
    }
}

// MARK: - Preview
#Preview {
    MainView(selectedTab: .constant("home"))
        .modelContainer(for: [Habit.self, HabitLog.self], inMemory: true)
}
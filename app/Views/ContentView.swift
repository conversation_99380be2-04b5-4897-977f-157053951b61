//
//  ContentView.swift
//  LightningHabit
//
//  Created by <PERSON><PERSON><PERSON><PERSON> on 2025. 07. 23..
//

import SwiftUI

struct ContentView: View {
    @State private var selectedTab = "home"
    @State private var showAddHabit = false
    @State private var isInitialized = false
    
    var body: some View {
        ZStack {
            if !isInitialized {
                LaunchScreenView()
                    .transition(.opacity)
            } else {
                // Content based on selected tab
                switch selectedTab {
                case "home":
                    MainView(selectedTab: $selectedTab)
                case "activity":
                    ActivityView(showHabitManagement: .constant(false))
                        .overlay(alignment: .bottom) {
                            BottomNavigationBar(
                                selectedTab: $selectedTab,
                                onAddTap: {
                                    showAddHabit = true
                                }
                            )
                        }
                default:
                    MainView(selectedTab: $selectedTab)
                }
            }
        }
        .background(Color.habitBackground)
        .sheet(isPresented: $showAddHabit) {
            AddEditHabitView()
        }
        .onAppear {
            // Delay to ensure SwiftData is ready
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
                withAnimation(.easeInOut(duration: 0.3)) {
                    isInitialized = true
                }
            }
        }
    }
}

#Preview {
    ContentView()
}